# CallQueueDataService Documentation

## Overview

The `CallQueueDataService` is a data abstraction layer that handles all CSV and data manipulations for call queue management. It provides a clean interface for data operations while being completely separate from call processing logic.

## Architecture

### Core Components

1. **`CallRecord`** - Data class representing a call record in storage
2. **`CallQueueDataService`** - Abstract base class defining the data interface
3. **`CSVCallQueueDataService`** - CSV-based implementation of the data service

### Design Principles

- **Separation of Concerns**: Data operations are completely separate from call processing
- **Abstraction**: Abstract base class allows for different data storage implementations
- **Type Safety**: Strong typing with dataclasses and type hints
- **Async Support**: All operations are async for better performance
- **Thread Safety**: CSV operations use locks to prevent concurrent access issues

## CallRecord Data Structure

```python
@dataclass
class CallRecord:
    row_index: int          # Unique identifier (1-based)
    phone_number: str       # Phone number to call
    question: str          # Question to ask
    answer: str = ""       # Answer received (empty if none)
    status: str = ""       # Call status (empty if pending)
    details: str = ""      # Additional details about the call
```

### Integration with CallItem

`CallRecord` provides seamless conversion to/from `CallItem` objects:

```python
# Convert CallRecord to CallItem for queue processing
call_item = record.to_call_item()

# Create CallRecord from CallItem
record = CallRecord.from_call_item(call_item)
```

## CallQueueDataService Interface

### Core Methods

```python
# Data Loading
async def load_all_records() -> List[CallRecord]
async def load_pending_records() -> List[CallRecord]
async def load_pending_call_items() -> List[CallItem]

# Data Updates
async def update_record_status(row_index: int, status: str, details: str = "") -> bool
async def update_record_answer(row_index: int, answer: str) -> bool
async def update_call_item_status(call_item: CallItem, status: CallStatus, details: str = "") -> bool

# Data Queries
async def get_record_by_index(row_index: int) -> Optional[CallRecord]
```

## CSV Implementation

### File Structure

The CSV file uses the following structure:

```csv
phone_number,question,answer,status,details
+1234567890,How satisfied are you?,Very satisfied,completed,Call finished at 2024-01-15 10:30:00
+1234567891,Would you recommend us?,,pending,
```

### Features

- **Automatic file creation** with proper headers
- **Column padding** ensures all rows have required columns
- **Concurrent access protection** using asyncio locks
- **Error handling** for file I/O operations
- **Data validation** ensures data integrity

### Usage Example

```python
from services.call_queue_data_service import CSVCallQueueDataService

# Initialize service
service = CSVCallQueueDataService("survey_data.csv")

# Add new records
row_index = await service.add_record("+1234567890", "How are you?")

# Load pending calls
pending_calls = await service.load_pending_call_items()

# Update status
await service.update_record_status(row_index, "completed", "Call finished")

# Get statistics
stats = await service.get_summary_stats()
```

## Integration with CallQueueHandler

The data service integrates seamlessly with the call queue handler:

```python
# Load pending calls from data service
pending_calls = await data_service.load_pending_call_items()

# Create status callback that updates data service
async def status_callback(call_item, status, details):
    await data_service.update_call_item_status(call_item, status, details)

# Process calls
handler = CallQueueHandler(
    agent_name="my-agent",
    sip_trunk_id="trunk-123",
    status_callback=status_callback
)

await handler.process_call_queue(pending_calls)
```

## Refactored Survey System

The `make_survey_calls.py` has been completely refactored to use the data service:

### Before (Direct CSV manipulation)
```python
# Old approach - direct CSV handling
async def read_csv_data():
    data = []
    with open(csv_file_path, 'r', newline='') as f:
        reader = csv.reader(f)
        # ... manual CSV parsing

async def update_csv_status(row_index, status, details=""):
    # ... manual CSV writing
```

### After (Data service abstraction)
```python
# New approach - clean data service
data_service = CSVCallQueueDataService(csv_file_path)

async def process_survey_calls():
    # Load pending calls using data service
    pending_calls = await data_service.load_pending_call_items()
    
    # Process with clean callback
    async def status_callback(call_item, status, details):
        await data_service.update_call_item_status(call_item, status, details)
```

## Benefits

### 1. Clean Separation of Concerns
- Data operations are isolated from call processing
- CSV logic is encapsulated in a dedicated service
- Easy to test data operations independently

### 2. Extensibility
- Abstract base class allows for different storage backends
- Easy to add database, API, or other data sources
- Consistent interface regardless of storage type

### 3. Type Safety and Validation
- Strong typing with dataclasses
- Automatic data validation
- Clear data contracts

### 4. Better Error Handling
- Centralized error handling for data operations
- Graceful handling of file I/O errors
- Atomic operations where possible

### 5. Performance
- Async operations for better concurrency
- Efficient CSV operations with minimal file I/O
- Thread-safe operations

## Extending the Data Service

### Custom Implementation Example

```python
class DatabaseCallQueueDataService(CallQueueDataService):
    """Database-based implementation"""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
    
    async def load_all_records(self) -> List[CallRecord]:
        # Implement database loading
        async with get_db_connection() as conn:
            rows = await conn.fetch("SELECT * FROM call_records")
            return [CallRecord(**row) for row in rows]
    
    async def update_record_status(self, row_index: int, status: str, details: str = "") -> bool:
        # Implement database update
        async with get_db_connection() as conn:
            result = await conn.execute(
                "UPDATE call_records SET status = $1, details = $2 WHERE id = $3",
                status, details, row_index
            )
            return result == "UPDATE 1"
```

### Enhanced CSV Service

```python
class EnhancedCSVDataService(CSVCallQueueDataService):
    """Enhanced CSV service with additional features"""
    
    async def get_records_by_status(self, status: str) -> List[CallRecord]:
        """Get all records with specific status"""
        all_records = await self.load_all_records()
        return [r for r in all_records if r.status == status]
    
    async def bulk_update_status(self, phone_numbers: List[str], status: str) -> int:
        """Update status for multiple phone numbers"""
        updated_count = 0
        for record in await self.load_all_records():
            if record.phone_number in phone_numbers:
                await self.update_record_status(record.row_index, status)
                updated_count += 1
        return updated_count
```

## Testing

The data service includes comprehensive tests covering:

- **Basic CRUD operations** - Create, read, update operations
- **Data conversion** - CallRecord ↔ CallItem conversion
- **Error handling** - Invalid files, missing records
- **Concurrency** - Multiple simultaneous operations
- **Integration** - Working with CallQueueHandler

Run tests with:
```bash
python -m pytest tests/test_call_queue_data_service.py -v
```

## Migration Guide

### From Direct CSV to Data Service

1. **Replace CSV imports**:
   ```python
   # Remove
   import csv
   
   # Add
   from services.call_queue_data_service import CSVCallQueueDataService
   ```

2. **Initialize data service**:
   ```python
   data_service = CSVCallQueueDataService("your_file.csv")
   ```

3. **Replace CSV operations**:
   ```python
   # Old
   with open(csv_file, 'r') as f:
       reader = csv.reader(f)
       # ... manual parsing
   
   # New
   records = await data_service.load_all_records()
   ```

4. **Update status callbacks**:
   ```python
   # Old
   async def callback(call_item, status, details):
       await update_csv_status(row_index, status, details)
   
   # New
   async def callback(call_item, status, details):
       await data_service.update_call_item_status(call_item, status, details)
   ```

## Future Enhancements

The data service architecture enables several future improvements:

1. **Database Support** - PostgreSQL, MySQL, SQLite implementations
2. **API Integration** - REST API or GraphQL data sources
3. **Caching** - Redis or in-memory caching for performance
4. **Data Validation** - Schema validation and data constraints
5. **Audit Logging** - Track all data changes
6. **Backup/Restore** - Automated data backup and recovery
7. **Data Migration** - Tools for moving between storage types

## Conclusion

The `CallQueueDataService` provides a clean, extensible, and robust foundation for call queue data management. It successfully separates data concerns from call processing logic while maintaining backward compatibility and enabling future enhancements.
