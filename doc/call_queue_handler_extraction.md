# Call Queue Handler Extraction

## Overview

This document describes the extraction of a new `CallQueueHandler` class from the `make_survey_calls.py` file. The goal was to create a reusable class that handles call queue management without being tied to CSV files or survey-specific logic.

## What Was Extracted

### Original Code Structure
The original `make_survey_calls.py` file contained:
- CSV file reading and writing logic
- Survey-specific call creation
- Call status monitoring
- Mixed concerns between data persistence and call management

### New Architecture

#### 1. CallQueueHandler Class (`src/services/call_queue_handler.py`)
A generic call queue management class that:
- **Manages call queues**: Processes lists of calls with configurable concurrency
- **Handles call lifecycle**: Creates agent dispatches and SIP participants
- **Monitors call status**: Tracks call progression (pending → dialing → ringing → active → completed)
- **Provides callbacks**: Allows custom handling of status changes
- **Is data-agnostic**: Works with any data source, not just CSV files

#### 2. Key Components

**CallItem Dataclass**:
```python
@dataclass
class CallItem:
    phone_number: str
    metadata: Dict[str, Any]
    call_id: str
    status: CallStatus = CallStatus.PENDING
    details: str = ""
```

**CallStatus Enum**:
```python
class CallStatus(Enum):
    PENDING = "pending"
    DIALING = "dialing"
    RINGING = "ringing"
    ACTIVE = "active"
    COMPLETED = "completed"
    TIMEOUT = "timeout"
    ERROR = "error"
```

**CallQueueHandler Class**:
- Configurable agent name, SIP trunk, and room naming
- Status callback mechanism for custom handling
- Concurrent call processing with semaphore control
- Automatic call monitoring and cleanup

## Benefits of the Extraction

### 1. Separation of Concerns
- **Call management logic** is now separate from **data persistence logic**
- CSV handling remains in `make_survey_calls.py`
- Call queue logic is reusable for other data sources

### 2. Reusability
The `CallQueueHandler` can now be used for:
- Database-driven call campaigns
- API-based call queues
- Different types of automated calls (not just surveys)
- Integration with external systems

### 3. Testability
- Easier to unit test call logic separately from CSV operations
- Mock-friendly design with dependency injection
- Clear interfaces and data structures

### 4. Flexibility
- Configurable concurrency limits
- Custom status callbacks
- Pluggable metadata handling
- Support for different call types

## Usage Examples

### Basic Usage
```python
from services.call_queue_handler import CallQueueHandler, CallItem, CallStatus

# Create call items
calls = [
    CallItem(
        phone_number="+1234567890",
        metadata={"purpose": "survey", "question": "How satisfied are you?"},
        call_id="call_001"
    )
]

# Create handler with callback
async def status_callback(call_item, status, details):
    print(f"Call {call_item.call_id}: {status.value}")

handler = CallQueueHandler(
    agent_name="my-agent",
    sip_trunk_id="trunk-123",
    status_callback=status_callback
)

# Process calls
await handler.process_call_queue(calls, max_concurrent=3)
```

### Survey Integration (Current)
The updated `make_survey_calls.py` now:
1. Reads CSV data
2. Creates `CallItem` objects
3. Uses `CallQueueHandler` to process calls
4. Updates CSV via status callback

### Database Integration (Example)
```python
async def database_callback(call_item, status, details):
    await db.execute(
        "UPDATE calls SET status = ?, details = ? WHERE id = ?",
        status.value, details, call_item.call_id
    )

# Load calls from database
calls = await load_calls_from_database()
handler = CallQueueHandler(
    agent_name="db-agent",
    sip_trunk_id="trunk-456",
    status_callback=database_callback
)
await handler.process_call_queue(calls)
```

## Files Modified

### New Files
- `src/services/call_queue_handler.py` - Main extracted class
- `src/examples/call_queue_example.py` - Usage examples
- `tests/test_call_queue_handler.py` - Unit tests
- `doc/call_queue_handler_extraction.md` - This documentation

### Modified Files
- `src/make_survey_calls.py` - Refactored to use CallQueueHandler
- `src/services/__init__.py` - Added exports for new classes

## Backward Compatibility

The `make_survey_calls.py` script maintains the same external interface:
- Same command-line usage
- Same CSV file format
- Same environment variables
- Same logging output

The refactoring is internal and doesn't break existing functionality.

## Future Enhancements

The extracted class enables several future improvements:

1. **Multiple Data Sources**: Easy integration with databases, APIs, message queues
2. **Advanced Scheduling**: Time-based call scheduling, retry logic
3. **Call Analytics**: Detailed metrics and reporting
4. **Load Balancing**: Distribution across multiple agents/trunks
5. **Integration**: Webhooks, external notifications, CRM systems

## Testing

The extraction includes comprehensive unit tests covering:
- CallItem creation and metadata handling
- CallQueueHandler initialization and configuration
- Status callback mechanisms (sync and async)
- Error handling and edge cases
- Concurrency control

Run tests with:
```bash
python -m pytest tests/test_call_queue_handler.py -v
```

## Conclusion

The extraction successfully separates call queue management from survey-specific logic, creating a reusable, testable, and flexible foundation for automated calling systems. The new architecture maintains backward compatibility while enabling future enhancements and integrations.
