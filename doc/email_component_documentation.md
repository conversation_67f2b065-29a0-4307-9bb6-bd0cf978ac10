# Email Collection Component Documentation

## Overview

The Email Collection Component is a comprehensive system extracted from the Richard.json prompt that provides structured email collection with validation, formatting, and professional conversation flow. It maintains the exact professional tone and approach from the original financial services outreach script.

## Extracted Instructions from <PERSON> Prompt

### Original Email Collection Section
The component extracts and structures these specific instructions from the Richard.json prompt:

**Transition Request:**
```
"Thank you for sharing all that. Based on what you've said, I think one of our partners could definitely offer some value as you move forward. To help them prepare and send you some relevant information beforehand, what would be the best email address for you? I'll just add it to your profile."
```

**Clarification Requests:**
1. "Could you please spell that out for me just to make sure I get it exactly right?"
2. "Sorry, I missed that last part, could you repeat the domain for me?"
3. "I think I missed that - could you spell out the part after the @ symbol?"

**Read-back Confirmation:**
```
"Okay, thank you. Let me just read that back to make sure I have it correct. That's {spelled_email}. Is that correct?"
```

**Success/Correction Messages:**
- Success: "Perfect, thank you. I've got that down."
- Correction: "My apologies. Could you please spell it out for me one more time? I want to ensure our partner can reach you."

## Components

### 1. EmailCollectionInstructions
**Dataclass containing structured conversation templates**

```python
@dataclass
class EmailCollectionInstructions:
    transition_request: str
    clarification_requests: List[str]
    readback_intro: str
    confirmation_success: str
    correction_request: str
```

### 2. EmailValidator
**Comprehensive email validation and formatting**

**Features:**
- **Format Validation** - RFC-compliant email format checking
- **Typo Correction** - Automatic correction of common email provider typos
- **Spelling Support** - Converts emails to spelled-out format for verbal confirmation
- **Domain Recognition** - Recognizes common email providers for simplified read-back

**Common Typo Corrections:**
```python
'gmai.com' → 'gmail.com'
'gmial.com' → 'gmail.com'
'outlok.com' → 'outlook.com'
'yahooo.com' → 'yahoo.com'
```

### 3. EmailCollectionComponent
**Complete email collection workflow management**

**State Management:**
- Tracks collection attempts (max 3)
- Manages conversation flow
- Handles confirmation/correction cycles
- Provides graceful fallback after max attempts

## Usage Examples

### Basic Email Validation

```python
from services.email_component import validate_email, spell_out_email

# Quick validation
result = validate_email("<EMAIL>")
print(f"Valid: {result.is_valid}")
print(f"Formatted: {result.formatted_email}")  # <EMAIL>
print(f"Suggestions: {result.suggestions}")    # Typo corrected

# Spelling for verbal confirmation
spelled = spell_out_email("<EMAIL>")
print(spelled)  # "J O H N dot D O E at gmail.com"
```

### Complete Email Collection Flow

```python
from services.email_component import EmailCollectionComponent

component = EmailCollectionComponent()

# Step 1: Initial request
print(component.get_initial_request())
# "Thank you for sharing all that. Based on what you've said..."

# Step 2: Process user input
result = component.process_email_input("<EMAIL>")
print(result['response_message'])
# "Okay, thank you. Let me just read that back..."

# Step 3: Handle confirmation
if result['next_action'] == 'readback_confirmation':
    confirmation = component.handle_confirmation_response(True, result['validation'].formatted_email)
    print(confirmation['response_message'])
    # "Perfect, thank you. I've got that down."
```

### Integration with Survey Agents

```python
from services.email_component import EmailCollectionComponent

class SurveyAgent:
    def __init__(self):
        self.email_component = EmailCollectionComponent()
    
    @function_tool
    async def collect_email(self, context: RunContext, email_input: str):
        result = self.email_component.process_email_input(email_input)
        
        if result['next_action'] == 'readback_confirmation':
            return None, result['response_message']
        elif result['next_action'] == 'request_clarification':
            return None, result['response_message']
        else:
            # Email collection complete or skipped
            return None, result['response_message']
```

## Validation Features

### Format Validation
- **RFC Compliance** - Follows email format standards
- **Length Limits** - Local part ≤64 chars, total ≤254 chars
- **Character Validation** - Allows standard email characters
- **Domain Validation** - Requires valid domain with extension

### Typo Correction
**Automatic correction of common mistakes:**

| Input | Corrected | Provider |
|-------|-----------|----------|
| <EMAIL> | <EMAIL> | Gmail |
| <EMAIL> | <EMAIL> | Outlook |
| <EMAIL> | <EMAIL> | Yahoo |
| <EMAIL> | <EMAIL> | Hotmail |

### Spelling Support
**Converts emails to verbal format:**

```python
"<EMAIL>" → "J O H N dot D O E at gmail.com"
"<EMAIL>" → "U S E R plus T A G at domain.co.uk"
"<EMAIL>" → "T E S T underscore U S E R at company-name.com"
```

## CLI Tools

### Email Component CLI
```bash
# Show extracted instructions
python tools/email_component_cli.py instructions

# Test validation with various emails
python tools/email_component_cli.py test-validation

# Test spelling functionality
python tools/email_component_cli.py test-spelling

# Interactive email collection simulation
python tools/email_component_cli.py interactive

# Validate specific email
python tools/email_component_cli.py validate "<EMAIL>"
```

### Interactive Simulation Example
```
Agent: Thank you for sharing all that. Based on what you've said, I think one of our partners could definitely offer some value as you move forward. To help them prepare and send you some relevant information beforehand, what would be the best email address for you?

User: <EMAIL>

Agent: Okay, thank you. Let me just read that back to make sure I have it correct. That's J O H N at gmail.com. Is that correct?

User: yes

Agent: Perfect, thank you. I've got that down.
```

## Function Tools for Agents

### Ready-to-Use Functions

```python
from tools.email_function_tool import collect_email_address, confirm_email_address

# In survey agent
@function_tool
async def collect_email(self, context: RunContext, email_input: str):
    response, action = collect_email_address(email_input, self.context)
    return None, response

@function_tool  
async def confirm_email(self, context: RunContext, confirmed: bool):
    response, action = confirm_email_address(confirmed, self.context)
    return None, response
```

### State Management
The function tools automatically manage:
- Collection attempts and retry logic
- Email validation and formatting
- Conversation state transitions
- Graceful fallback after max attempts

## Error Handling

### Validation Errors
```python
result = validate_email("invalid-email")
print(result.issues)     # ['Missing @ symbol']
print(result.suggestions) # ['Email should contain @ symbol']
```

### Collection Failures
- **Max Attempts Reached** - Gracefully skip email collection
- **Invalid Format** - Request clarification with helpful prompts
- **Typo Detection** - Automatic correction with confirmation
- **User Confusion** - Multiple clarification strategies

## Integration Examples

### With Fintel Survey
```python
# Add email collection to fintel_qualification survey
questions.append({
    'id': 'email_address',
    'text': 'To help our partners prepare and send you some relevant information beforehand, what would be the best email address for you?',
    'type': 'email',  # Custom type for email collection
    'required': True,
    'validation': 'email_component'
})
```

### With Call Queue System
```python
# Update call metadata with collected email
async def email_collection_callback(call_item, email):
    call_item.metadata['email_address'] = email
    await data_service.update_record_status(
        call_item.metadata['row_index'], 
        'email_collected', 
        f'Email: {email}'
    )
```

## Professional Benefits

### Maintains Richard's Professional Tone
- **Consultative Approach** - "Based on what you've said..."
- **Value Proposition** - "...could definitely offer some value..."
- **Professional Courtesy** - "I'll just add it to your profile"
- **Quality Assurance** - "Let me read that back to make sure..."

### Reduces Collection Errors
- **Typo Prevention** - Automatic correction of common mistakes
- **Verbal Confirmation** - Spelled-out read-back prevents misunderstandings
- **Multiple Attempts** - Up to 3 tries with different clarification approaches
- **Graceful Fallback** - Professional handling when collection fails

### Improves Lead Quality
- **Validated Emails** - Ensures deliverable email addresses
- **Formatted Consistently** - Standardized email format for CRM systems
- **Professional Experience** - Maintains prospect confidence
- **Follow-up Ready** - Clean email data for partner handoff

## Technical Specifications

### Dependencies
- **No External Dependencies** - Uses only Python standard library
- **Regex Validation** - RFC-compliant email pattern matching
- **Dataclass Structure** - Type-safe configuration management

### Performance
- **Fast Validation** - Regex-based validation in microseconds
- **Memory Efficient** - Minimal state management
- **Thread Safe** - No shared mutable state
- **Scalable** - Suitable for high-volume call campaigns

### Extensibility
- **Custom Providers** - Easy to add new email provider typo corrections
- **Configurable Attempts** - Adjustable retry limits
- **Custom Messages** - Replaceable conversation templates
- **Validation Rules** - Extensible validation criteria

## Conclusion

The Email Collection Component successfully extracts and structures the professional email collection approach from the Richard.json prompt, providing a robust, validated, and conversation-aware system for gathering email addresses in phone surveys. It maintains the consultative tone and quality assurance approach that makes the original script effective while adding technical validation and error handling capabilities.
