# Survey Agent Update Documentation

## Overview

The survey agent has been completely updated to support named surveys with YAML-based configurations. The new system provides a flexible, extensible framework for conducting multi-question surveys with proper session management and progress tracking.

## Key Changes

### ✅ **Survey Service Architecture**

**New Components:**
- **`SurveyService`** - Manages survey definitions and user sessions
- **`SurveyDefinition`** - Represents complete survey configurations
- **`SurveyQuestion`** - Individual question definitions with types and options
- **`UserAnswers`** - Session management for participant responses
- **`UserAnswer`** - Individual answer tracking with timestamps

### ✅ **YAML-Based Survey Definitions**

Surveys are now defined in YAML files with rich metadata:

```yaml
title: "Customer Satisfaction Survey"
description: "A brief survey to understand customer satisfaction"
introduction: "Thank you for taking the time to participate"
conclusion: "Thank you for your feedback!"
max_duration_minutes: 5

questions:
  - id: "satisfaction_rating"
    text: "On a scale of 1 to 10, how satisfied are you?"
    type: "rating"
    options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]
    required: true
    order: 1

  - id: "recommendation"
    text: "Would you recommend our service?"
    type: "yes_no"
    options: ["Yes", "No"]
    required: true
    order: 2
```

### ✅ **Updated SurveyAgent**

**Before (Single Question):**
```python
class SurveyAgent(Agent):
    def __init__(self, question="Do you prefer chocolate or vanilla?", ...):
        self.survey_question = question
        # Single question handling
```

**After (Multi-Question Survey):**
```python
class SurveyAgent(Agent):
    def __init__(self, survey_name: str = "simple_preference", ...):
        self.survey_name = survey_name
        self.survey: SurveyDefinition = None
        self.user_answers: UserAnswers = None
        self.current_question = None
        
    async def initialize_survey(self):
        # Load survey definition and create user session
```

## Survey Types and Features

### Question Types

1. **Text Questions**
   ```yaml
   - id: "feedback"
     text: "What could we improve?"
     type: "text"
     required: false
   ```

2. **Multiple Choice**
   ```yaml
   - id: "preference"
     text: "What's your favorite feature?"
     type: "multiple_choice"
     options: ["Speed", "Design", "Features", "Support"]
     required: true
   ```

3. **Rating Scale**
   ```yaml
   - id: "satisfaction"
     text: "Rate your satisfaction (1-10)"
     type: "rating"
     options: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"]
     required: true
   ```

4. **Yes/No Questions**
   ```yaml
   - id: "recommend"
     text: "Would you recommend us?"
     type: "yes_no"
     options: ["Yes", "No"]
     required: true
   ```

### Built-in Surveys

The system includes built-in surveys that work without YAML files:

1. **`simple_preference`** - Single ice cream preference question
2. **`customer_satisfaction`** - Two-question satisfaction survey

## Session Management

### UserAnswers Class

Manages participant responses throughout the survey:

```python
@dataclass
class UserAnswers:
    survey_name: str
    participant_id: str
    answers: Dict[str, UserAnswer]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    current_question_index: int = 0
```

**Key Features:**
- **Progress Tracking** - Completion percentage and question navigation
- **Answer Validation** - Ensures required questions are answered
- **Timestamp Management** - Tracks when answers are recorded
- **Session Persistence** - Maintains state during call session

### Question Flow

1. **Initialize Survey** - Load definition and create user session
2. **Get Next Question** - Retrieve next unanswered question in order
3. **Record Answer** - Save user response with timestamp
4. **Check Completion** - Determine if all required questions answered
5. **Progress to Next** - Continue to next question or conclude survey

## Integration with Call System

### Updated Metadata

Call items now include survey information:

```python
call_item = CallItem(
    phone_number="+1234567890",
    metadata={
        'survey_name': 'customer_satisfaction',  # New: Survey identifier
        'row_index': 1,
        'question': 'Legacy field'  # Maintained for backward compatibility
    },
    call_id="1"
)
```

### Agent Initialization

```python
# Create agent with survey name
agent = SurveyAgent(survey_name="customer_satisfaction", context=context, job_context=ctx)

# Initialize survey and user session
await agent.initialize_survey()
```

### Dynamic Instructions

The agent's instructions are updated dynamically based on the current question:

```python
def _update_instructions(self):
    question_text = self.current_question.text
    survey_title = self.survey.title
    
    # Build context-aware instructions
    self.instructions = f"""
        You are conducting "{survey_title}".
        Ask this question: "{question_text}"
        {options_text}  # Dynamic based on question type
    """
```

## Enhanced Call Processing

### Multi-Question Flow

The `record_survey_answer` function now handles:

1. **Answer Recording** - Save response to survey service
2. **Progress Check** - Determine if more questions remain
3. **Next Question** - Load and present next question
4. **Survey Completion** - Handle conclusion and call termination

```python
@function_tool
async def record_survey_answer(self, context: RunContext, answer: str):
    # Record answer in survey service
    await survey_service.record_answer(survey_name, participant_id, question_id, answer)
    
    # Check completion
    if await survey_service.is_survey_complete(survey_name, participant_id):
        # Survey complete - end call
        return None, f"{survey.conclusion} [Call ended]"
    else:
        # Get next question
        next_question = await survey_service.get_next_question(survey_name, participant_id)
        self.current_question = next_question
        return None, f"Thank you. Now, {next_question.text}"
```

## Command Line Interface

### Updated make_survey_calls.py

```bash
# List available surveys
python make_survey_calls.py --list-surveys

# Use specific survey
python make_survey_calls.py --survey customer_satisfaction

# Default to simple_preference
python make_survey_calls.py
```

### Survey Management

```python
# Load and validate survey
survey = await survey_service.load_survey(survey_name)
if not survey:
    logger.error(f"Survey '{survey_name}' not found")
    return

# Add survey name to call metadata
for call_item in pending_call_items:
    call_item.metadata['survey_name'] = survey_name
```

## Backward Compatibility

### Legacy Support

The system maintains compatibility with existing single-question calls:

1. **Metadata Detection** - If `question` field exists but no `survey_name`, defaults to `simple_preference`
2. **CSV Structure** - Existing CSV files continue to work
3. **Agent Interface** - Old call creation methods still function

### Migration Path

Existing surveys can be gradually migrated:

1. **Phase 1** - Use built-in surveys with existing CSV data
2. **Phase 2** - Create YAML files for custom surveys
3. **Phase 3** - Enhance with advanced question types and logic

## Examples and Usage

### Creating Custom Surveys

1. **Create YAML file** in `src/surveys/` directory:
   ```yaml
   title: "Product Feedback Survey"
   description: "Help us improve our products"
   questions:
     - id: "usage_frequency"
       text: "How often do you use our product?"
       type: "multiple_choice"
       options: ["Daily", "Weekly", "Monthly", "Rarely"]
       required: true
   ```

2. **Use in calls**:
   ```bash
   python make_survey_calls.py --survey product_feedback
   ```

### Programmatic Usage

```python
# Initialize service
survey_service = SurveyService()

# Load survey
survey = await survey_service.load_survey("customer_satisfaction")

# Create user session
session = await survey_service.create_user_session("customer_satisfaction", "user123")

# Process questions
while not await survey_service.is_survey_complete("customer_satisfaction", "user123"):
    question = await survey_service.get_next_question("customer_satisfaction", "user123")
    # Present question to user and get answer
    await survey_service.record_answer("customer_satisfaction", "user123", question.id, answer)
```

## Testing and Validation

### Comprehensive Test Suite

- **Unit Tests** - Individual component testing
- **Integration Tests** - End-to-end survey flow
- **Backward Compatibility** - Legacy system support
- **Error Handling** - Graceful degradation

### Built-in Fallbacks

- **YAML Unavailable** - Uses built-in survey definitions
- **File Not Found** - Falls back to built-in surveys
- **Invalid Survey** - Error handling with user feedback

## Benefits

### 1. **Flexibility**
- Support for multiple question types
- Dynamic survey flow based on responses
- Easy addition of new surveys without code changes

### 2. **Scalability**
- Session management for concurrent users
- Progress tracking and resumption capability
- Efficient caching of survey definitions

### 3. **Maintainability**
- Clear separation between survey logic and call handling
- YAML-based configuration for non-technical users
- Comprehensive error handling and logging

### 4. **User Experience**
- Natural conversation flow with context-aware instructions
- Proper survey introduction and conclusion
- Progress indication and completion feedback

## Future Enhancements

The new architecture enables several advanced features:

1. **Conditional Logic** - Skip questions based on previous answers
2. **Survey Branching** - Different paths based on responses
3. **Multi-language Support** - Localized survey definitions
4. **Analytics Integration** - Response analysis and reporting
5. **Real-time Updates** - Dynamic survey modification
6. **Advanced Question Types** - Date, numeric range, file upload

## Conclusion

The updated survey agent provides a robust, flexible foundation for conducting sophisticated phone surveys while maintaining full backward compatibility with existing systems. The YAML-based configuration system makes it easy to create and modify surveys without code changes, while the session management ensures reliable tracking of user responses across multi-question surveys.
