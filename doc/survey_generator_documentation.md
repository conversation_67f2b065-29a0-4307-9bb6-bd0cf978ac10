# Survey Generator Documentation

## Overview

The Survey Generator is a powerful tool that automatically creates survey YAML files from LLM prompts and natural language descriptions. It uses pattern matching and natural language processing to extract questions and generate properly formatted survey configurations.

## Installation and Setup

### Prerequisites

PyYAML is required for full functionality:

```bash
# Install PyYAML
python3 -m pip install --break-system-packages PyYAML

# Install python-dotenv for environment variable support
python3 -m pip install --break-system-packages python-dotenv
```

### Verification

Test that PyYAML is working:

```bash
python3 -c "import yaml; print('✅ PyYAML is working!'); print(f'Version: {yaml.__version__}')"
```

## Components

### 1. SurveyGenerator Class

**Location:** `src/services/survey_generator.py`

**Key Features:**
- **Question Extraction** - Uses regex patterns to identify questions in text
- **Type Detection** - Automatically determines question types (text, multiple_choice, yes_no, rating)
- **Option Parsing** - Extracts answer options from question context
- **Category Classification** - Assigns categories based on question content
- **YAML Generation** - Creates properly formatted survey YAML files

**Question Types Detected:**
- **Text Questions** - Open-ended responses
- **Yes/No Questions** - Binary choices with indicators like "yes or no", "y/n"
- **Multiple Choice** - Questions with "choose", "select", "or" indicators
- **Rating Questions** - Scale-based questions with "rate", "scale", "1 to X" patterns

### 2. CLI Tool

**Location:** `src/tools/survey_generator_cli.py`

**Commands Available:**

#### Generate from File
```bash
# Generate from JSON prompt file
python survey_generator_cli.py from-file amber.json --output economic_survey.yaml

# Preview without saving
python survey_generator_cli.py from-file amber.json --preview

# Custom survey name
python survey_generator_cli.py from-file prompt.txt --name my_survey
```

#### Generate from Text
```bash
# Generate from text prompt
python survey_generator_cli.py from-text "What is your favorite color? Do you prefer cats or dogs?" --name preferences

# With custom metadata
python survey_generator_cli.py from-text "Rate our service (1-10)?" --name feedback --title "Service Feedback" --description "Customer feedback survey"
```

#### Analyze Prompts
```bash
# Analyze what questions would be extracted
python survey_generator_cli.py analyze amber.json
```

#### Interactive Mode
```bash
# Interactive survey creation
python survey_generator_cli.py interactive
```

## Usage Examples

### 1. Basic Text Generation

```bash
cd src
python tools/survey_generator_cli.py from-text \
  "What is your favorite programming language? Do you prefer Python or JavaScript? How satisfied are you with your current job?" \
  --name tech_survey \
  --preview
```

**Output:**
```yaml
title: Tech Survey
description: Survey generated from LLM prompt with 3 questions
introduction: Thank you for participating in our survey.
conclusion: Thank you for your time and responses!
max_duration_minutes: 6
questions:
  - id: q1_favorite_programming
    text: "What is your favorite programming language?"
    type: text
    required: true
    order: 1
```

### 2. Generate from JSON Prompt

```bash
python tools/survey_generator_cli.py from-file meta_test/amber.json \
  --name economic_survey \
  --output surveys/economic_survey.yaml
```

### 3. Interactive Creation

```bash
python tools/survey_generator_cli.py interactive
```

**Interactive Flow:**
1. Enter survey questions line by line
2. Type 'done' when finished
3. Provide survey name and metadata
4. Choose to save or preview

## Generated Survey Structure

### YAML Format

```yaml
title: "Survey Title"
description: "Survey description"
introduction: "Welcome message"
conclusion: "Thank you message"
max_duration_minutes: 5

questions:
  - id: "question_id"
    text: "Question text?"
    type: "question_type"
    options: ["Option 1", "Option 2"]  # For multiple choice
    required: true
    order: 1
    category: "category_name"
```

### Question Categories

The generator automatically assigns categories:

- **demographics** - Age, gender, location, education, income
- **satisfaction** - Satisfaction, happiness, rating, experience
- **preferences** - Preferences, favorites, choices
- **behavior** - Actions, habits, behaviors
- **opinion** - Thoughts, beliefs, feelings
- **economic** - Money, cost, price, investment, financial
- **product** - Product, service, feature, quality
- **future** - Future plans, expectations, predictions

## Integration with Survey System

### 1. Generated Surveys are Automatically Available

Once generated, surveys appear in the survey system:

```bash
# List all surveys (including generated ones)
python tools/survey_cli.py list

# Test generated survey
python tools/survey_cli.py details my_generated_survey

# Use in calls
python make_survey_calls.py --survey my_generated_survey
```

### 2. YAML File Loading

Generated YAML files are automatically loaded by the SurveyService:

```python
# Load generated survey
survey_service = SurveyService()
survey = await survey_service.load_survey("my_generated_survey")
```

## Advanced Features

### 1. Pattern Matching

The generator uses sophisticated regex patterns to identify questions:

```python
question_patterns = [
    r'"([^"]*\?)"',                    # Quoted questions
    r'(?:Ask|Question|Q\d*[:.]\s*)([^.!]*\?)',  # Labeled questions
    r'([^.!]*\?\s*(?:Yes or no|Y/N))', # Yes/no questions
    r'([^.!]*\?[^.!]*(?:or|,)[^.!]*\?)', # Multiple choice
]
```

### 2. Option Extraction

Automatically extracts options from:
- **"or" patterns** - "Do you prefer A or B?"
- **Parenthetical options** - "Choose (option1, option2, option3)"
- **Context clues** - Options mentioned near questions
- **Rating scales** - "Rate 1 to 10" → ["1", "2", ..., "10"]

### 3. Duplicate Detection

Prevents duplicate questions from being included in the final survey.

### 4. Fallback Support

Works even without PyYAML by using built-in survey definitions.

## Real-World Example: Economic Survey

### Input (amber.json excerpt):
```json
{
  "llm": {
    "prompt": "Economic Impact: - \"Have you experienced an increase in the cost of essential goods or services like groceries? Yes or no?\" Spending Habits: - \"Have you changed the way you shop or prioritize purchases because of inflation? Yes or no?\""
  }
}
```

### Generated Output:
```yaml
title: "Economic Survey"
description: "Economic research survey"
questions:
  - id: "economic_impact"
    text: "Have you experienced an increase in the cost of essential goods or services like groceries?"
    type: "yes_no"
    options: ["Yes", "No"]
    required: true
    order: 1
    category: "economic"
    
  - id: "spending_habits"
    text: "Have you changed the way you shop or prioritize purchases because of inflation?"
    type: "yes_no"
    options: ["Yes", "No"]
    required: true
    order: 2
    category: "behavior"
```

## Best Practices

### 1. Input Preparation

**Good Input:**
- Clear, well-formed questions with question marks
- Consistent formatting
- Explicit options for multiple choice questions

**Example:**
```
What is your favorite color?
Do you prefer cats or dogs?
Rate your satisfaction from 1 to 5.
Are you employed? Yes or no.
```

### 2. Review Generated Output

Always review generated surveys before use:
- Check for duplicate questions
- Verify question types are correct
- Ensure options are properly extracted
- Adjust categories if needed

### 3. Manual Refinement

Generated surveys can be manually edited:
- Fix question wording
- Add missing options
- Adjust question order
- Enhance metadata

## Limitations and Improvements

### Current Limitations

1. **Complex Questions** - May not handle multi-part questions well
2. **Context Dependency** - Some questions need surrounding context
3. **Duplicate Detection** - Could be more sophisticated
4. **Option Extraction** - May miss implicit options

### Future Improvements

1. **LLM Integration** - Use actual LLMs for better question understanding
2. **Conditional Logic** - Support for question branching
3. **Validation** - Better question quality validation
4. **Templates** - Pre-built survey templates
5. **Batch Processing** - Process multiple prompt files at once

## Troubleshooting

### Common Issues

1. **No Questions Extracted**
   - Check that input contains question marks
   - Verify questions are properly formatted
   - Try the analyze command to see what's detected

2. **Poor Question Quality**
   - Review and manually edit generated YAML
   - Improve input prompt formatting
   - Use more explicit question structures

3. **Missing Options**
   - Add options manually to YAML file
   - Improve option indicators in input text
   - Use parenthetical option lists

### Debug Commands

```bash
# Analyze what would be extracted
python survey_generator_cli.py analyze input.json

# Preview without saving
python survey_generator_cli.py from-file input.json --preview

# Test generated survey
python survey_cli.py details generated_survey_name
```

## Conclusion

The Survey Generator provides a powerful way to automatically create survey configurations from natural language prompts. While it may require some manual refinement for complex surveys, it significantly speeds up the survey creation process and ensures consistent formatting. The integration with the broader survey system makes generated surveys immediately usable for phone call campaigns.
