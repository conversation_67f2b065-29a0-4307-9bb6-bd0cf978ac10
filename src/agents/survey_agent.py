import asyncio
import logging
from pathlib import Path

from livekit.agents import Agent, function_tool, RunContext
from livekit.plugins import deepgram, openai, silero
from livekit.protocol.room import DeleteRoomRequest

from services.survey_service import SurveyService, SurveyDefinition, UserAnswers
from services.call_queue_data_service import CSVCallQueueDataService
from services.email_component import EmailCollectionComponent

logger = logging.getLogger("survey-agent")
logger.setLevel(logging.INFO)


class SurveyAgent(Agent):
    """
    Advanced survey agent with multi-question support, email collection, and professional conversation flow.
    
    Features:
    - YAML-based survey definitions
    - Multi-question survey progression
    - Professional email collection with validation
    - Session management and progress tracking
    - Dynamic instruction generation based on question types
    - Graceful error handling and fallbacks
    """
    
    def __init__(self, survey_name: str = "simple_preference", context=None, job_context=None, 
                 survey_service=None, data_service=None) -> None:
        self.survey_name = survey_name
        self.context = context or {}
        self.job_context = job_context
        self.phone_number = self.context.get("phone_number", "unknown")
        self.row_index = self.context.get("row_index", 1)
        
        # Initialize survey and user session
        self.survey: SurveyDefinition = None
        self.user_answers: UserAnswers = None
        self.current_question = None
        
        # Email collection component for email-type questions
        self.email_component = EmailCollectionComponent()
        
        # Services (can be injected for testing)
        self.survey_service = survey_service or SurveyService()
        self.data_service = data_service or CSVCallQueueDataService(Path(__file__).parent.parent / "survey_data.csv")
        
        # This will be set during session initialization
        self.instructions = ""
        
        super().__init__(
            instructions=self.instructions,  # Will be updated in initialize_survey
            stt=deepgram.STT(),
            llm=openai.LLM(model="gpt-4o"),
            tts=openai.TTS(),
            vad=silero.VAD.load()
        )
    
    async def initialize_survey(self):
        """Initialize the survey and user session"""
        try:
            # Load survey definition
            self.survey = await self.survey_service.load_survey(self.survey_name)
            if not self.survey:
                logger.error(f"Failed to load survey: {self.survey_name}")
                return False
            
            # Create or get user session
            participant_id = f"phone_{self.phone_number}_{self.row_index}"
            self.user_answers = await self.survey_service.get_or_create_session(
                self.survey_name, participant_id
            )
            
            if not self.user_answers:
                logger.error(f"Failed to create user session for survey: {self.survey_name}")
                return False
            
            # Get the first/next question
            self.current_question = await self.survey_service.get_next_question(
                self.survey_name, participant_id
            )
            
            if not self.current_question:
                logger.warning(f"No questions available for survey: {self.survey_name}")
                return False
            
            # Update instructions with survey-specific information
            self._update_instructions()
            
            logger.info(f"Initialized survey '{self.survey_name}' for participant '{participant_id}'")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing survey: {e}")
            return False
    
    def _update_instructions(self):
        """Update agent instructions based on current survey state"""
        if not self.survey or not self.current_question:
            return
        
        question_text = self.current_question.text
        survey_title = self.survey.title
        introduction = self.survey.introduction
        
        # Handle different question types
        if self.current_question.type == "email":
            # Use email component to generate instructions
            if not self.email_component.is_collecting():
                # Start email collection
                self.email_component.start_email_collection(self.current_question.text)
            
            self.instructions = self.email_component.get_agent_instructions(survey_title, introduction)
        else:
            # Build options text for other question types
            options_text = ""
            if self.current_question.type == "multiple_choice" and self.current_question.options:
                options_list = ", ".join(self.current_question.options)
                options_text = f" The available options are: {options_list}."
            elif self.current_question.type == "yes_no":
                options_text = " Please answer with yes or no."
            elif self.current_question.type == "rating":
                if self.current_question.options:
                    options_text = f" Please rate from {self.current_question.options[0]} to {self.current_question.options[-1]}."
                else:
                    options_text = " Please provide a rating."

            self.instructions = f"""
                You are conducting a phone survey called "{survey_title}".

                {introduction}

                Your current task is to ask this specific question:
                "{question_text}"{options_text}

                Be polite and professional. Introduce yourself as a survey caller named "Sam".
                Ask the question clearly and wait for their response. Keep the conversation focused
                on getting their answer to this specific question.

                When you receive their answer, use the `record_survey_answer` function to save it.

                If they ask about the survey, you can mention it's about "{survey_title}" and should
                only take a few minutes of their time.
            """
        
        # Update the agent's instructions
        if hasattr(self, '_llm') and self._llm:
            # Update the system prompt if the LLM is already initialized
            pass  # The instructions will be used in the next interaction
    
    @function_tool
    async def collect_email_address(self, context: RunContext, email_input: str):
        """Collect and validate email address using the email component"""
        logger.info(f"Collecting email address: {email_input}")
        
        if not self.current_question or self.current_question.type != "email":
            return None, "I'm not currently collecting an email address."
        
        try:
            # Process the email input using the email component
            result = self.email_component.process_email_input(email_input)
            
            # Update instructions if state changed
            if result.get('state_changed', False):
                self._update_instructions()
            
            if result['next_action'] == 'readback_confirmation':
                return None, result['response_message']
            
            elif result['next_action'] == 'request_clarification':
                return None, result['response_message']
            
            elif result['next_action'] == 'skip_email':
                # Email collection completed (failed), move to next question
                await self._handle_email_completion(None)
                return None, result['response_message']
            
            else:
                return None, "I'm having trouble processing that email address. Could you please try again?"
                
        except Exception as e:
            logger.error(f"Error collecting email: {e}")
            return None, "I'm having trouble with the email collection. Could you please repeat your email address?"
    
    @function_tool
    async def confirm_email_address(self, context: RunContext, confirmed: bool):
        """Confirm the spelled-out email address"""
        logger.info(f"Email confirmation: {confirmed}")
        
        if not self.email_component.is_collecting():
            return None, "I'm not currently confirming an email address."
        
        try:
            # Process the confirmation using the email component
            result = self.email_component.handle_confirmation_response(confirmed)
            
            # Update instructions if state changed
            if result.get('state_changed', False):
                self._update_instructions()
            
            if result['success']:
                # Email successfully collected
                final_email = result['final_email']
                await self._handle_email_completion(final_email)
                return None, result['response_message']
            
            elif result['next_action'] == 'request_correction':
                # User said email is wrong, ask for correction
                return None, result['response_message']
            
            elif result['next_action'] == 'skip_email':
                # Max attempts reached, skip email
                await self._handle_email_completion(None)
                return None, result['response_message']
            
            else:
                return None, "I'm having trouble with the email confirmation. Let's continue with the survey."
                    
        except Exception as e:
            logger.error(f"Error confirming email: {e}")
            return None, "I'm having trouble with the email confirmation. Let's continue with the survey."
    
    async def _handle_email_completion(self, email: str):
        """Handle completion of email collection (success or skip)"""
        try:
            participant_id = f"phone_{self.phone_number}_{self.row_index}"
            
            if email:
                # Email successfully collected
                logger.info(f"Email collected successfully: {email}")
                await self.survey_service.record_answer(
                    self.survey_name, participant_id, self.current_question.id, email
                )
                
                # Update CSV with email
                await self.data_service.update_record_answer(self.row_index, email)
            else:
                # Email collection skipped
                logger.info("Email collection skipped")
                await self.survey_service.record_answer(
                    self.survey_name, participant_id, self.current_question.id, "[Email not provided]"
                )
            
            # Reset email component for next use
            self.email_component.reset()
            
            # Move to next question or complete survey
            await self._proceed_to_next_question()
            
        except Exception as e:
            logger.error(f"Error handling email completion: {e}")
    
    async def _proceed_to_next_question(self):
        """Move to the next question or complete the survey"""
        try:
            participant_id = f"phone_{self.phone_number}_{self.row_index}"
            
            # Check if survey is complete
            is_complete = await self.survey_service.is_survey_complete(self.survey_name, participant_id)
            
            if is_complete:
                # Survey completed
                self.user_answers.mark_completed()
                await self.data_service.update_record_status(
                    self.row_index, "completed", 
                    f"Survey completed at {self.user_answers.completed_at}"
                )
                
                conclusion = self.survey.conclusion if self.survey.conclusion else "Thank you for completing our survey!"
                
                # End the call after a brief delay
                await asyncio.sleep(3)
                await self.job_context.api.room.delete_room(DeleteRoomRequest(
                    room=self.job_context.room.name
                ))
                
                return conclusion + " [Call ended]"
            
            else:
                # Get next question
                next_question = await self.survey_service.get_next_question(self.survey_name, participant_id)
                
                if next_question:
                    self.current_question = next_question
                    
                    # Reset email collection for new question
                    if next_question.type == "email":
                        self.email_component.reset()
                    
                    self._update_instructions()
                    
                    return f"Thank you. Now, {next_question.text}"
                else:
                    # No more questions (shouldn't happen if is_complete logic is correct)
                    await self.data_service.update_record_status(
                        self.row_index, "completed", "All questions answered"
                    )
                    
                    await asyncio.sleep(3)
                    await self.job_context.api.room.delete_room(DeleteRoomRequest(
                        room=self.job_context.room.name
                    ))
                    
                    return "Thank you for your time! [Call ended]"
                    
        except Exception as e:
            logger.error(f"Error proceeding to next question: {e}")
            return "Thank you for your time. [Call ended]"
