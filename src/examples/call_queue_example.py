#!/usr/bin/env python3
"""
Example demonstrating how to use CallQueueHandler independently of CSV files or surveys.
This shows how the extracted class can be used for any type of call queue management.
"""

import asyncio
import logging
import os
from pathlib import Path
from dotenv import load_dotenv
from services.call_queue_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CallItem, CallStatus

# Load environment variables
load_dotenv(dotenv_path=Path(__file__).parent.parent.parent / '.env')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def simple_status_callback(call_item: CallItem, status: CallStatus, details: str):
    """Simple callback that just logs status changes"""
    logger.info(f"📞 Call {call_item.call_id} to {call_item.phone_number}: {status.value} - {details}")


async def database_status_callback(call_item: CallItem, status: CallStatus, details: str):
    """Example callback that could update a database instead of CSV"""
    # This is where you would update your database
    logger.info(f"🗄️  Would update database: Call {call_item.call_id} status = {status.value}")
    
    # Example database update (pseudo-code):
    # await db.execute(
    #     "UPDATE calls SET status = ?, details = ?, updated_at = NOW() WHERE id = ?",
    #     status.value, details, call_item.call_id
    # )


async def example_basic_usage():
    """Example of basic CallQueueHandler usage"""
    logger.info("=== Basic Usage Example ===")
    
    # Configuration
    agent_name = "example-agent"
    sip_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
    
    if not sip_trunk_id:
        logger.error("SIP_OUTBOUND_TRUNK_ID not set in environment")
        return
    
    # Create some example calls
    call_items = [
        CallItem(
            phone_number="+**********",
            metadata={"purpose": "customer_survey", "campaign_id": "camp_001"},
            call_id="call_001"
        ),
        CallItem(
            phone_number="+**********", 
            metadata={"purpose": "appointment_reminder", "patient_id": "pat_123"},
            call_id="call_002"
        ),
        CallItem(
            phone_number="+**********",
            metadata={"purpose": "delivery_notification", "order_id": "ord_456"},
            call_id="call_003"
        )
    ]
    
    # Create call handler with simple logging callback
    call_handler = CallQueueHandler(
        agent_name=agent_name,
        sip_trunk_id=sip_trunk_id,
        room_name_prefix="example-call-",
        status_callback=simple_status_callback,
        monitor_timeout=60  # Shorter timeout for example
    )
    
    # Process calls sequentially
    logger.info(f"Processing {len(call_items)} calls sequentially...")
    await call_handler.process_call_queue(call_items, max_concurrent=1)
    
    logger.info("Basic usage example completed!")


async def example_concurrent_calls():
    """Example of processing multiple calls concurrently"""
    logger.info("=== Concurrent Calls Example ===")
    
    agent_name = "concurrent-agent"
    sip_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
    
    if not sip_trunk_id:
        logger.error("SIP_OUTBOUND_TRUNK_ID not set in environment")
        return
    
    # Create multiple calls for concurrent processing
    call_items = []
    for i in range(5):
        call_items.append(CallItem(
            phone_number=f"+123456789{i}",
            metadata={"batch": "concurrent_test", "index": i},
            call_id=f"concurrent_call_{i:03d}"
        ))
    
    # Create call handler with database callback
    call_handler = CallQueueHandler(
        agent_name=agent_name,
        sip_trunk_id=sip_trunk_id,
        room_name_prefix="concurrent-call-",
        status_callback=database_status_callback,
        monitor_timeout=60
    )
    
    # Process calls with concurrency limit of 3
    logger.info(f"Processing {len(call_items)} calls with max concurrency of 3...")
    await call_handler.process_call_queue(call_items, max_concurrent=3)
    
    logger.info("Concurrent calls example completed!")


async def example_custom_metadata():
    """Example showing how to use custom metadata for different use cases"""
    logger.info("=== Custom Metadata Example ===")
    
    agent_name = "metadata-agent"
    sip_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
    
    if not sip_trunk_id:
        logger.error("SIP_OUTBOUND_TRUNK_ID not set in environment")
        return
    
    # Different types of calls with rich metadata
    call_items = [
        # Healthcare appointment reminder
        CallItem(
            phone_number="+**********",
            metadata={
                "type": "appointment_reminder",
                "patient_name": "John Doe",
                "appointment_date": "2024-01-15",
                "doctor": "Dr. Smith",
                "department": "Cardiology"
            },
            call_id="appt_001"
        ),
        
        # E-commerce order update
        CallItem(
            phone_number="+**********",
            metadata={
                "type": "order_update",
                "customer_name": "Jane Smith",
                "order_number": "ORD-12345",
                "status": "shipped",
                "tracking_number": "TRK-67890"
            },
            call_id="order_001"
        ),
        
        # Survey call
        CallItem(
            phone_number="+**********",
            metadata={
                "type": "survey",
                "survey_name": "Customer Satisfaction Q4 2024",
                "questions": ["How satisfied are you?", "Would you recommend us?"],
                "estimated_duration": "5 minutes"
            },
            call_id="survey_001"
        )
    ]
    
    async def metadata_callback(call_item: CallItem, status: CallStatus, details: str):
        """Callback that shows how to use metadata"""
        call_type = call_item.metadata.get("type", "unknown")
        logger.info(f"📋 {call_type.upper()} call {call_item.call_id}: {status.value}")
        
        if status == CallStatus.ACTIVE:
            # Different handling based on call type
            if call_type == "appointment_reminder":
                logger.info(f"   📅 Reminding {call_item.metadata.get('patient_name')} about appointment")
            elif call_type == "order_update":
                logger.info(f"   📦 Updating {call_item.metadata.get('customer_name')} about order {call_item.metadata.get('order_number')}")
            elif call_type == "survey":
                logger.info(f"   📊 Starting survey: {call_item.metadata.get('survey_name')}")
    
    call_handler = CallQueueHandler(
        agent_name=agent_name,
        sip_trunk_id=sip_trunk_id,
        room_name_prefix="metadata-call-",
        status_callback=metadata_callback,
        monitor_timeout=60
    )
    
    logger.info(f"Processing {len(call_items)} calls with custom metadata...")
    await call_handler.process_call_queue(call_items, max_concurrent=2)
    
    logger.info("Custom metadata example completed!")


async def main():
    """Run all examples"""
    logger.info("🚀 Starting CallQueueHandler examples...")
    
    try:
        await example_basic_usage()
        await asyncio.sleep(2)  # Brief pause between examples
        
        await example_concurrent_calls()
        await asyncio.sleep(2)
        
        await example_custom_metadata()
        
    except Exception as e:
        logger.error(f"Error running examples: {e}")
    
    logger.info("✅ All examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
