#!/usr/bin/env python3
"""
Example script showing how to use RoomsApiService to monitor call status
"""

import asyncio
import logging
from pathlib import Path
import sys

# Add src to path so we can import our modules
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.rooms_api_service import RoomsApiService
from dotenv import load_dotenv

# Load environment variables
load_dotenv(dotenv_path=Path(__file__).parent.parent.parent / '.env')

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def show_all_rooms():
    """Show all rooms in the LiveKit instance"""
    print("\n" + "="*60)
    print("ALL ROOMS")
    print("="*60)
    
    async with RoomsApiService() as rooms_service:
        rooms = await rooms_service.list_rooms()
        
        if not rooms:
            print("No rooms found")
            return
        
        for room in rooms:
            print(f"Room: {room.name}")
            print(f"  Created: {room.creation_time}")
            print(f"  Participants: {room.num_participants}")
            print(f"  Metadata: {room.metadata[:100]}..." if len(room.metadata) > 100 else f"  Metadata: {room.metadata}")
            print()

async def show_survey_calls():
    """Show all survey call rooms and their status"""
    print("\n" + "="*60)
    print("SURVEY CALL STATUS")
    print("="*60)
    
    async with RoomsApiService() as rooms_service:
        calls = await rooms_service.get_calls_by_pattern("survey-call-")
        
        if not calls:
            print("No survey calls found")
            return
        
        for call in calls:
            print(f"Room: {call.room_name}")
            print(f"  Call Status: {call.call_status or 'unknown'}")
            print(f"  Participants: {call.num_participants}")
            
            if call.phone_participant:
                print(f"  Phone User:")
                print(f"    Identity: {call.phone_participant.identity}")
                print(f"    Call Status: {call.phone_participant.call_status}")
                print(f"    Attributes: {call.phone_participant.attributes}")
            
            print(f"  All Participants:")
            for participant in call.participants:
                print(f"    - {participant.identity}: {participant.call_status or 'N/A'}")
            print()

async def monitor_specific_room(room_name: str, duration: int = 60):
    """Monitor a specific room for status changes"""
    print(f"\n" + "="*60)
    print(f"MONITORING ROOM: {room_name}")
    print(f"Duration: {duration} seconds")
    print("="*60)
    
    async with RoomsApiService() as rooms_service:
        status_changes = await rooms_service.monitor_room_status(
            room_name=room_name,
            timeout_seconds=duration
        )
        
        if status_changes:
            print("Status changes detected:")
            for timestamp, status in status_changes:
                import datetime
                dt = datetime.datetime.fromtimestamp(timestamp)
                print(f"  {dt.strftime('%H:%M:%S')} - {status}")
        else:
            print("No status changes detected during monitoring period")

async def cleanup_completed_calls():
    """Clean up completed survey call rooms"""
    print("\n" + "="*60)
    print("CLEANING UP COMPLETED CALLS")
    print("="*60)
    
    async with RoomsApiService() as rooms_service:
        cleaned_count = await rooms_service.cleanup_rooms_by_status(
            room_name_pattern="survey-call-",
            cleanup_statuses=['hangup', 'completed']
        )
        print(f"Cleaned up {cleaned_count} completed call rooms")

async def get_room_details(room_name: str):
    """Get detailed information about a specific room"""
    print(f"\n" + "="*60)
    print(f"ROOM DETAILS: {room_name}")
    print("="*60)
    
    async with RoomsApiService() as rooms_service:
        call_info = await rooms_service.get_call_info(room_name)
        
        if not call_info:
            print(f"Room '{room_name}' not found")
            return
        
        print(f"Room Name: {call_info.room_name}")
        print(f"Created: {call_info.room_creation_time}")
        print(f"Participants: {call_info.num_participants}")
        print(f"Call Status: {call_info.call_status or 'unknown'}")
        
        if call_info.phone_participant:
            print(f"\nPhone Participant:")
            print(f"  Identity: {call_info.phone_participant.identity}")
            print(f"  Name: {call_info.phone_participant.name}")
            print(f"  Call Status: {call_info.phone_participant.call_status}")
            print(f"  Joined At: {call_info.phone_participant.joined_at}")
            print(f"  Attributes:")
            for key, value in call_info.phone_participant.attributes.items():
                print(f"    {key}: {value}")
        
        print(f"\nAll Participants:")
        for participant in call_info.participants:
            print(f"  - {participant.identity} ({participant.name})")
            print(f"    Status: {participant.call_status or 'N/A'}")
            print(f"    Joined: {participant.joined_at}")

async def main():
    """Main function with command line interface"""
    import argparse
    
    parser = argparse.ArgumentParser(description="LiveKit Rooms API Service Examples")
    parser.add_argument("--all-rooms", action="store_true", help="Show all rooms")
    parser.add_argument("--survey-calls", action="store_true", help="Show survey call status")
    parser.add_argument("--monitor", type=str, help="Monitor specific room")
    parser.add_argument("--duration", type=int, default=60, help="Monitoring duration in seconds")
    parser.add_argument("--cleanup", action="store_true", help="Clean up completed calls")
    parser.add_argument("--room-details", type=str, help="Get details for specific room")
    
    args = parser.parse_args()
    
    if args.all_rooms:
        await show_all_rooms()
    
    if args.survey_calls:
        await show_survey_calls()
    
    if args.monitor:
        await monitor_specific_room(args.monitor, args.duration)
    
    if args.cleanup:
        await cleanup_completed_calls()
    
    if args.room_details:
        await get_room_details(args.room_details)
    
    if not any([args.all_rooms, args.survey_calls, args.monitor, args.cleanup, args.room_details]):
        # Default: show survey calls
        await show_survey_calls()

if __name__ == "__main__":
    asyncio.run(main())
