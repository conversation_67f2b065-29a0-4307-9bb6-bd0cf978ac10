#!/usr/bin/env python3
"""
Example demonstrating how to use CallQueueDataService for different data operations.
This shows how the data layer can be used independently of the call queue processing.
"""

import asyncio
import logging
from pathlib import Path
from services.call_queue_data_service import CSVCallQueueDataService, CallRecord
from services.call_queue_handler import CallItem, CallStatus

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_basic_data_operations():
    """Example of basic data service operations"""
    logger.info("=== Basic Data Operations Example ===")
    
    # Create a temporary CSV file for testing
    test_csv_path = Path(__file__).parent / "test_data.csv"
    data_service = CSVCallQueueDataService(test_csv_path)
    
    # Add some test records
    logger.info("Adding test records...")
    row1 = await data_service.add_record("+1234567890", "What's your favorite color?")
    row2 = await data_service.add_record("+1234567891", "How satisfied are you with our service?")
    row3 = await data_service.add_record("+1234567892", "Would you recommend us to a friend?")
    
    logger.info(f"Added records at rows: {row1}, {row2}, {row3}")
    
    # Load all records
    all_records = await data_service.load_all_records()
    logger.info(f"Total records: {len(all_records)}")
    for record in all_records:
        logger.info(f"  Row {record.row_index}: {record.phone_number} - {record.question}")
    
    # Load pending records
    pending_records = await data_service.load_pending_records()
    logger.info(f"Pending records: {len(pending_records)}")
    
    # Update some statuses
    await data_service.update_record_status(row1, "dialing", "Call initiated")
    await data_service.update_record_status(row2, "completed", "Call finished successfully")
    await data_service.update_record_answer(row3, "Yes, definitely!")
    
    # Check pending again
    pending_after_updates = await data_service.load_pending_records()
    logger.info(f"Pending records after updates: {len(pending_after_updates)}")
    
    # Get summary stats
    stats = await data_service.get_summary_stats()
    logger.info(f"Summary stats: {stats}")
    
    # Clean up test file
    if test_csv_path.exists():
        test_csv_path.unlink()
    
    logger.info("Basic data operations example completed!")


async def example_call_item_integration():
    """Example showing integration between CallRecord and CallItem"""
    logger.info("=== CallItem Integration Example ===")
    
    test_csv_path = Path(__file__).parent / "test_integration.csv"
    data_service = CSVCallQueueDataService(test_csv_path)
    
    # Add test data
    await data_service.add_record("+1555000001", "Rate our customer service (1-10)")
    await data_service.add_record("+1555000002", "What features would you like to see?")
    
    # Load as CallItem objects
    call_items = await data_service.load_pending_call_items()
    logger.info(f"Loaded {len(call_items)} CallItem objects")
    
    for call_item in call_items:
        logger.info(f"CallItem {call_item.call_id}:")
        logger.info(f"  Phone: {call_item.phone_number}")
        logger.info(f"  Question: {call_item.metadata.get('question')}")
        logger.info(f"  Status: {call_item.status.value}")
        
        # Simulate status updates using CallItem
        await data_service.update_call_item_status(call_item, CallStatus.DIALING, "Starting call")
        await data_service.update_call_item_status(call_item, CallStatus.ACTIVE, "Call answered")
        await data_service.update_call_item_status(call_item, CallStatus.COMPLETED, "Call finished")
    
    # Verify updates
    final_stats = await data_service.get_summary_stats()
    logger.info(f"Final stats: {final_stats}")
    
    # Clean up
    if test_csv_path.exists():
        test_csv_path.unlink()
    
    logger.info("CallItem integration example completed!")


async def example_data_migration():
    """Example showing how to migrate data between different formats"""
    logger.info("=== Data Migration Example ===")
    
    # Create source and destination data services
    source_csv = Path(__file__).parent / "source_data.csv"
    dest_csv = Path(__file__).parent / "dest_data.csv"
    
    source_service = CSVCallQueueDataService(source_csv)
    dest_service = CSVCallQueueDataService(dest_csv)
    
    # Populate source with test data
    logger.info("Creating source data...")
    await source_service.add_record("+1111111111", "Original question 1")
    await source_service.add_record("+2222222222", "Original question 2")
    await source_service.add_record("+3333333333", "Original question 3")
    
    # Update some records in source
    await source_service.update_record_status(1, "completed", "Finished")
    await source_service.update_record_answer(2, "Great service!")
    
    # Migrate data
    logger.info("Migrating data...")
    source_records = await source_service.load_all_records()
    
    for record in source_records:
        # Add to destination
        new_row = await dest_service.add_record(record.phone_number, record.question)
        
        # Copy status and answer if they exist
        if record.status:
            await dest_service.update_record_status(new_row, record.status, record.details)
        if record.answer:
            await dest_service.update_record_answer(new_row, record.answer)
    
    # Verify migration
    dest_records = await dest_service.load_all_records()
    logger.info(f"Migrated {len(dest_records)} records")
    
    source_stats = await source_service.get_summary_stats()
    dest_stats = await dest_service.get_summary_stats()
    
    logger.info(f"Source stats: {source_stats}")
    logger.info(f"Destination stats: {dest_stats}")
    
    # Clean up
    for path in [source_csv, dest_csv]:
        if path.exists():
            path.unlink()
    
    logger.info("Data migration example completed!")


async def example_custom_data_service():
    """Example showing how to extend the data service for custom needs"""
    logger.info("=== Custom Data Service Example ===")
    
    class EnhancedCSVDataService(CSVCallQueueDataService):
        """Enhanced CSV data service with additional features"""
        
        async def get_records_by_status(self, status: str):
            """Get all records with a specific status"""
            all_records = await self.load_all_records()
            return [r for r in all_records if r.status == status]
        
        async def get_records_by_phone_prefix(self, prefix: str):
            """Get records by phone number prefix"""
            all_records = await self.load_all_records()
            return [r for r in all_records if r.phone_number.startswith(prefix)]
        
        async def bulk_update_status(self, phone_numbers: list, status: str, details: str = ""):
            """Update status for multiple phone numbers"""
            all_records = await self.load_all_records()
            updated_count = 0
            
            for record in all_records:
                if record.phone_number in phone_numbers:
                    await self.update_record_status(record.row_index, status, details)
                    updated_count += 1
            
            return updated_count
    
    # Use the enhanced service
    test_csv = Path(__file__).parent / "enhanced_test.csv"
    enhanced_service = EnhancedCSVDataService(test_csv)
    
    # Add test data
    await enhanced_service.add_record("+1555000001", "Question 1")
    await enhanced_service.add_record("+1555000002", "Question 2")
    await enhanced_service.add_record("+1444000001", "Question 3")
    await enhanced_service.add_record("+1444000002", "Question 4")
    
    # Test custom methods
    prefix_555_records = await enhanced_service.get_records_by_phone_prefix("+1555")
    logger.info(f"Records with +1555 prefix: {len(prefix_555_records)}")
    
    # Bulk update
    phone_numbers = ["+1555000001", "+1555000002"]
    updated_count = await enhanced_service.bulk_update_status(
        phone_numbers, "dialing", "Bulk update test"
    )
    logger.info(f"Bulk updated {updated_count} records")
    
    # Check results
    dialing_records = await enhanced_service.get_records_by_status("dialing")
    logger.info(f"Records with 'dialing' status: {len(dialing_records)}")
    
    # Clean up
    if test_csv.exists():
        test_csv.unlink()
    
    logger.info("Custom data service example completed!")


async def main():
    """Run all examples"""
    logger.info("🚀 Starting CallQueueDataService examples...")
    
    try:
        await example_basic_data_operations()
        await asyncio.sleep(1)
        
        await example_call_item_integration()
        await asyncio.sleep(1)
        
        await example_data_migration()
        await asyncio.sleep(1)
        
        await example_custom_data_service()
        
    except Exception as e:
        logger.error(f"Error running examples: {e}")
        raise
    
    logger.info("✅ All data service examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
