#!/usr/bin/env python3
"""
Example demonstrating the new survey service functionality.
Shows how to create surveys, manage user sessions, and track progress.
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.survey_service import SurveyService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_basic_survey_operations():
    """Example of basic survey service operations"""
    logger.info("=== Basic Survey Operations Example ===")
    
    # Initialize survey service
    survey_service = SurveyService()
    
    # List available surveys
    available_surveys = await survey_service.list_available_surveys()
    logger.info(f"Available surveys: {available_surveys}")
    
    # Load a specific survey
    survey_name = "customer_satisfaction"
    survey = await survey_service.load_survey(survey_name)
    
    if survey:
        logger.info(f"Loaded survey: {survey.title}")
        logger.info(f"Description: {survey.description}")
        logger.info(f"Questions: {len(survey.questions)}")
        
        for i, question in enumerate(survey.get_questions_in_order(), 1):
            logger.info(f"  Q{i}: {question.text} (Type: {question.type}, Required: {question.required})")
    else:
        logger.error(f"Failed to load survey: {survey_name}")


async def example_user_session_management():
    """Example of managing user survey sessions"""
    logger.info("=== User Session Management Example ===")
    
    survey_service = SurveyService()
    survey_name = "product_feedback"
    participant_id = "user_12345"
    
    # Create user session
    session = await survey_service.create_user_session(survey_name, participant_id)
    
    if not session:
        logger.error(f"Failed to create session for survey: {survey_name}")
        return
    
    logger.info(f"Created session for participant: {participant_id}")
    
    # Get survey progress
    progress = await survey_service.get_survey_progress(survey_name, participant_id)
    logger.info(f"Initial progress: {progress['completion_percentage']:.1f}%")
    
    # Simulate answering questions
    survey = await survey_service.load_survey(survey_name)
    questions = survey.get_questions_in_order()
    
    # Answer first question
    first_question = questions[0]
    logger.info(f"Answering question: {first_question.text}")
    await survey_service.record_answer(survey_name, participant_id, first_question.id, "Daily")
    
    # Check progress
    progress = await survey_service.get_survey_progress(survey_name, participant_id)
    logger.info(f"Progress after first answer: {progress['completion_percentage']:.1f}%")
    
    # Get next question
    next_question = await survey_service.get_next_question(survey_name, participant_id)
    if next_question:
        logger.info(f"Next question: {next_question.text}")
        await survey_service.record_answer(survey_name, participant_id, next_question.id, "4 - Easy")
    
    # Check if survey is complete
    is_complete = await survey_service.is_survey_complete(survey_name, participant_id)
    logger.info(f"Survey complete: {is_complete}")
    
    # Final progress
    progress = await survey_service.get_survey_progress(survey_name, participant_id)
    logger.info(f"Final progress: {progress}")


async def example_survey_flow_simulation():
    """Example simulating a complete survey flow"""
    logger.info("=== Complete Survey Flow Simulation ===")
    
    survey_service = SurveyService()
    survey_name = "simple_preference"
    participant_id = "phone_+1234567890_1"
    
    # Load survey
    survey = await survey_service.load_survey(survey_name)
    if not survey:
        logger.error(f"Survey '{survey_name}' not found")
        return
    
    logger.info(f"Starting survey: {survey.title}")
    logger.info(f"Introduction: {survey.introduction}")
    
    # Create session
    session = await survey_service.create_user_session(survey_name, participant_id)
    
    # Simulate complete survey flow
    while True:
        # Get next question
        current_question = await survey_service.get_next_question(survey_name, participant_id)
        
        if not current_question:
            logger.info("No more questions!")
            break
        
        logger.info(f"\nCurrent question: {current_question.text}")
        
        if current_question.type == "multiple_choice" and current_question.options:
            logger.info(f"Options: {', '.join(current_question.options)}")
        
        # Simulate user answer based on question type
        if current_question.id == "ice_cream_preference":
            answer = "Chocolate"
        else:
            answer = "Sample answer"
        
        logger.info(f"User answers: {answer}")
        
        # Record the answer
        await survey_service.record_answer(survey_name, participant_id, current_question.id, answer)
        
        # Check progress
        progress = await survey_service.get_survey_progress(survey_name, participant_id)
        logger.info(f"Progress: {progress['completion_percentage']:.1f}%")
        
        # Check if complete
        if await survey_service.is_survey_complete(survey_name, participant_id):
            logger.info(f"\n✅ Survey completed!")
            logger.info(f"Conclusion: {survey.conclusion}")
            break
    
    # Show final session state
    final_session = await survey_service.get_user_session(survey_name, participant_id)
    logger.info(f"\nFinal session state:")
    logger.info(f"  Started: {final_session.started_at}")
    logger.info(f"  Answers recorded: {len([a for a in final_session.answers.values() if a.is_complete])}")
    
    for question_id, answer in final_session.answers.items():
        if answer.is_complete:
            logger.info(f"  {question_id}: {answer.answer}")


async def example_multiple_participants():
    """Example with multiple participants taking the same survey"""
    logger.info("=== Multiple Participants Example ===")
    
    survey_service = SurveyService()
    survey_name = "customer_satisfaction"
    
    participants = [
        ("user_001", ["8", "Yes", "Better mobile app", "Fast response", "Email"]),
        ("user_002", ["9", "Yes", "More features", "User interface", "Phone"]),
        ("user_003", ["6", "No", "Price reduction", "Customer support", "No follow-up needed"])
    ]
    
    survey = await survey_service.load_survey(survey_name)
    if not survey:
        logger.error(f"Survey '{survey_name}' not found")
        return
    
    questions = survey.get_questions_in_order()
    
    for participant_id, answers in participants:
        logger.info(f"\nProcessing participant: {participant_id}")
        
        # Create session
        await survey_service.create_user_session(survey_name, participant_id)
        
        # Answer all questions
        for i, answer in enumerate(answers):
            if i < len(questions):
                question = questions[i]
                await survey_service.record_answer(survey_name, participant_id, question.id, answer)
                logger.info(f"  {question.text} -> {answer}")
        
        # Check completion
        is_complete = await survey_service.is_survey_complete(survey_name, participant_id)
        progress = await survey_service.get_survey_progress(survey_name, participant_id)
        
        logger.info(f"  Complete: {is_complete}, Progress: {progress['completion_percentage']:.1f}%")


async def example_survey_analytics():
    """Example showing how to analyze survey responses"""
    logger.info("=== Survey Analytics Example ===")
    
    survey_service = SurveyService()
    survey_name = "customer_satisfaction"
    
    # Simulate some responses first
    participants_data = [
        ("analytics_user_1", ["9", "Yes", "Mobile app", "Speed", "Email"]),
        ("analytics_user_2", ["7", "Yes", "Documentation", "Features", "Phone"]),
        ("analytics_user_3", ["8", "Yes", "Price", "Support", "Email"]),
        ("analytics_user_4", ["6", "No", "Quality", "Interface", "No follow-up needed"])
    ]
    
    survey = await survey_service.load_survey(survey_name)
    questions = survey.get_questions_in_order()
    
    # Collect responses
    for participant_id, answers in participants_data:
        await survey_service.create_user_session(survey_name, participant_id)
        for i, answer in enumerate(answers):
            if i < len(questions):
                await survey_service.record_answer(survey_name, participant_id, questions[i].id, answer)
    
    # Analyze responses (simplified example)
    logger.info(f"Survey Analytics for '{survey.title}':")
    logger.info(f"Total participants: {len(participants_data)}")
    
    # Get all sessions (in a real implementation, you'd have a method for this)
    satisfaction_scores = []
    recommendations = {"Yes": 0, "No": 0}
    
    for participant_id, _ in participants_data:
        session = await survey_service.get_user_session(survey_name, participant_id)
        
        # Satisfaction rating
        if "satisfaction_rating" in session.answers:
            score = session.answers["satisfaction_rating"].answer
            if score.isdigit():
                satisfaction_scores.append(int(score))
        
        # Recommendation
        if "recommendation" in session.answers:
            rec = session.answers["recommendation"].answer
            if rec in recommendations:
                recommendations[rec] += 1
    
    # Calculate metrics
    if satisfaction_scores:
        avg_satisfaction = sum(satisfaction_scores) / len(satisfaction_scores)
        logger.info(f"Average satisfaction: {avg_satisfaction:.1f}/10")
    
    total_recs = sum(recommendations.values())
    if total_recs > 0:
        rec_rate = (recommendations["Yes"] / total_recs) * 100
        logger.info(f"Recommendation rate: {rec_rate:.1f}%")
    
    logger.info(f"Recommendation breakdown: {recommendations}")


async def main():
    """Run all examples"""
    logger.info("🚀 Starting Survey Service examples...")
    
    try:
        await example_basic_survey_operations()
        await asyncio.sleep(1)
        
        await example_user_session_management()
        await asyncio.sleep(1)
        
        await example_survey_flow_simulation()
        await asyncio.sleep(1)
        
        await example_multiple_participants()
        await asyncio.sleep(1)
        
        await example_survey_analytics()
        
    except Exception as e:
        logger.error(f"Error running examples: {e}")
        raise
    
    logger.info("✅ All survey service examples completed!")


if __name__ == "__main__":
    asyncio.run(main())
