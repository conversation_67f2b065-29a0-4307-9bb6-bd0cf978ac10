import asyncio
import json
import logging

from dotenv import load_dotenv
from livekit.agents import cli, AgentSession, WorkerOptions, JobContext

from agents.survey_agent import SurveyAgent

load_dotenv(dotenv_path='.env')

logger = logging.getLogger("calling-agent")
logger.setLevel(logging.INFO)


async def entrypoint(ctx: JobContext):
    """Main entrypoint for survey calls"""
    await ctx.connect()

    metadata_json = ctx.job.metadata
    logger.info(f"Received metadata: {metadata_json}")

    metadata = json.loads(metadata_json)
    phone_number = metadata.get("phone_number", "unknown")
    row_index = metadata.get("row_index", 1)

    # Get survey name from metadata, default to simple_preference
    survey_name = metadata.get("survey_name", "simple_preference")

    # Legacy support: if 'question' is provided but no survey_name, use simple_preference
    if "question" in metadata and "survey_name" not in metadata:
        logger.info("Legacy question format detected, using simple_preference survey")
        survey_name = "simple_preference"

    logger.info(f"Parsed metadata - phone_number: {phone_number}, row_index: {row_index}, survey_name: {survey_name}")

    context = {
        "phone_number": phone_number,
        "row_index": row_index
    }

    # Create and initialize the survey agent
    agent = SurveyAgent(survey_name=survey_name, context=context, job_context=ctx)

    # Initialize the survey
    if not await agent.initialize_survey():
        logger.error(f"Failed to initialize survey '{survey_name}' for call {row_index}")
        return

    session = AgentSession()

    await session.start(
        agent=agent,
        room=ctx.room
    )


if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, agent_name="survey-agent"))
