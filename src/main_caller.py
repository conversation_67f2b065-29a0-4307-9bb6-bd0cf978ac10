import asyncio
import json
import logging
from pathlib import Path

import pandas as pd
from dotenv import load_dotenv
from livekit.agents import Agent, cli, function_tool, RunContext, AgentSession, WorkerOptions, JobContext
from livekit.plugins import deepgram, openai, silero
from livekit.protocol.room import DeleteRoomRequest

from services.survey_service import SurveyService, SurveyDefinition, UserAnswers
from services.call_queue_data_service import CSVCallQueueDataService
from services.email_component import EmailCollectionComponent

load_dotenv(dotenv_path='.env')

logger = logging.getLogger("calling-agent")
logger.setLevel(logging.INFO)

csv_file_path = Path(__file__).parent / "survey_data.csv"

# Initialize services
survey_service = SurveyService()
data_service = CSVCallQueueDataService(csv_file_path)

class SurveyAgent(Agent):
    def __init__(self, survey_name: str = "simple_preference", context=None, job_context=None) -> None:
        self.survey_name = survey_name
        self.context = context or {}
        self.job_context = job_context
        self.phone_number = self.context.get("phone_number", "unknown")
        self.row_index = self.context.get("row_index", 1)

        # Initialize survey and user session
        self.survey: SurveyDefinition = None
        self.user_answers: UserAnswers = None
        self.current_question = None

        # Email collection component for email-type questions
        self.email_component = EmailCollectionComponent()

        # This will be set during session initialization
        self.instructions = ""

        super().__init__(
            instructions=self.instructions,  # Will be updated in initialize_survey
            stt=deepgram.STT(),
            llm=openai.LLM(model="gpt-4o"),
            tts=openai.TTS(),
            vad=silero.VAD.load()
        )

    async def initialize_survey(self):
        """Initialize the survey and user session"""
        try:
            # Load survey definition
            self.survey = await survey_service.load_survey(self.survey_name)
            if not self.survey:
                logger.error(f"Failed to load survey: {self.survey_name}")
                return False

            # Create or get user session
            participant_id = f"phone_{self.phone_number}_{self.row_index}"
            self.user_answers = await survey_service.get_or_create_session(
                self.survey_name, participant_id
            )

            if not self.user_answers:
                logger.error(f"Failed to create user session for survey: {self.survey_name}")
                return False

            # Get the first/next question
            self.current_question = await survey_service.get_next_question(
                self.survey_name, participant_id
            )

            if not self.current_question:
                logger.warning(f"No questions available for survey: {self.survey_name}")
                return False

            # Update instructions with survey-specific information
            self._update_instructions()

            logger.info(f"Initialized survey '{self.survey_name}' for participant '{participant_id}'")
            return True

        except Exception as e:
            logger.error(f"Error initializing survey: {e}")
            return False

    def _update_instructions(self):
        """Update agent instructions based on current survey state"""
        if not self.survey or not self.current_question:
            return

        question_text = self.current_question.text
        survey_title = self.survey.title
        introduction = self.survey.introduction

        # Handle different question types
        if self.current_question.type == "email":
            # Special handling for email questions
            if self.email_collection_state == "none":
                # Use the professional email request from Richard prompt
                email_request = self.email_component.get_initial_request()
                self.instructions = f"""
                    You are conducting a phone survey called "{survey_title}".

                    {introduction}

                    You have reached the email collection part of the survey. Use this exact message:
                    "{email_request}"

                    After asking, listen for their email address and use the `collect_email_address` function to process it.

                    Be professional and patient. If they provide an unclear email, you may need to ask for clarification.
                """
            elif self.email_collection_state == "confirming":
                self.instructions = f"""
                    You are confirming an email address with the participant.

                    You have just read back their email address in spelled-out format.
                    Wait for their confirmation (yes/no) and use the `confirm_email_address` function.

                    Be patient and professional. If they say it's incorrect, you'll ask them to spell it again.
                """
            else:
                # Collecting state - asking for clarification
                self.instructions = f"""
                    You are collecting an email address from the participant.

                    You have asked for clarification of their email address.
                    Listen carefully for their response and use the `collect_email_address` function.

                    Be patient and professional. Take your time to get the email address correct.
                """
        else:
            # Build options text for other question types
            options_text = ""
            if self.current_question.type == "multiple_choice" and self.current_question.options:
                options_list = ", ".join(self.current_question.options)
                options_text = f" The available options are: {options_list}."
            elif self.current_question.type == "yes_no":
                options_text = " Please answer with yes or no."
            elif self.current_question.type == "rating":
                if self.current_question.options:
                    options_text = f" Please rate from {self.current_question.options[0]} to {self.current_question.options[-1]}."
                else:
                    options_text = " Please provide a rating."

            self.instructions = f"""
                You are conducting a phone survey called "{survey_title}".

                {introduction}

                Your current task is to ask this specific question:
                "{question_text}"{options_text}

                Be polite and professional. Introduce yourself as a survey caller named "Sam".
                Ask the question clearly and wait for their response. Keep the conversation focused
                on getting their answer to this specific question.

                When you receive their answer, use the `record_survey_answer` function to save it.

                If they ask about the survey, you can mention it's about "{survey_title}" and should
                only take a few minutes of their time.
            """

        # Update the agent's instructions
        if hasattr(self, '_llm') and self._llm:
            # Update the system prompt if the LLM is already initialized
            pass  # The instructions will be used in the next interaction

    @function_tool
    async def collect_email_address(self, context: RunContext, email_input: str):
        """Collect and validate email address using the email component"""
        logger.info(f"Collecting email address: {email_input}")

        if not self.current_question or self.current_question.type != "email":
            return None, "I'm not currently collecting an email address."

        try:
            # Process the email input
            result = self.email_component.process_email_input(email_input)

            if result['next_action'] == 'readback_confirmation':
                # Email validated, need confirmation
                self.email_collection_state = "confirming"
                self.pending_email = result['validation'].formatted_email
                self._update_instructions()  # Update instructions for confirmation state

                return None, result['response_message']

            elif result['next_action'] == 'request_clarification':
                # Need clarification
                self.email_collection_state = "collecting"
                self._update_instructions()  # Update instructions for collecting state

                return None, result['response_message']

            elif result['next_action'] == 'skip_email':
                # Max attempts reached, skip email
                self.email_collection_state = "none"

                # Move to next question or complete survey
                await self._handle_email_completion(None)

                return None, result['response_message']

            else:
                return None, "I'm having trouble processing that email address. Could you please try again?"

        except Exception as e:
            logger.error(f"Error collecting email: {e}")
            return None, "I'm having trouble with the email collection. Could you please repeat your email address?"

    @function_tool
    async def confirm_email_address(self, context: RunContext, confirmed: bool):
        """Confirm the spelled-out email address"""
        logger.info(f"Email confirmation: {confirmed}")

        if self.email_collection_state != "confirming" or not self.pending_email:
            return None, "I'm not currently confirming an email address."

        try:
            # Process the confirmation
            result = self.email_component.handle_confirmation_response(confirmed, self.pending_email)

            if result['success']:
                # Email successfully collected
                final_email = result['final_email']
                self.email_collection_state = "none"

                # Record the email and move to next question
                await self._handle_email_completion(final_email)

                return None, result['response_message']

            else:
                if result.get('next_action') == 'request_correction':
                    # User said email is wrong, ask for correction
                    self.email_collection_state = "collecting"
                    self.pending_email = None
                    self._update_instructions()

                    return None, result['response_message']
                else:
                    # Max attempts reached, skip email
                    self.email_collection_state = "none"
                    await self._handle_email_completion(None)

                    return None, result['response_message']

        except Exception as e:
            logger.error(f"Error confirming email: {e}")
            return None, "I'm having trouble with the email confirmation. Let's continue with the survey."

    async def _handle_email_completion(self, email: str):
        """Handle completion of email collection (success or skip)"""
        try:
            participant_id = f"phone_{self.phone_number}_{self.row_index}"

            if email:
                # Email successfully collected
                logger.info(f"Email collected successfully: {email}")
                await survey_service.record_answer(
                    self.survey_name, participant_id, self.current_question.id, email
                )

                # Update CSV with email
                await data_service.update_record_answer(self.row_index, email)
            else:
                # Email collection skipped
                logger.info("Email collection skipped")
                await survey_service.record_answer(
                    self.survey_name, participant_id, self.current_question.id, "[Email not provided]"
                )

            # Reset email component for next use
            self.email_component.reset()
            self.pending_email = None

            # Move to next question or complete survey
            await self._proceed_to_next_question()

        except Exception as e:
            logger.error(f"Error handling email completion: {e}")

    async def _proceed_to_next_question(self):
        """Move to the next question or complete the survey"""
        try:
            participant_id = f"phone_{self.phone_number}_{self.row_index}"

            # Check if survey is complete
            is_complete = await survey_service.is_survey_complete(self.survey_name, participant_id)

            if is_complete:
                # Survey completed
                self.user_answers.mark_completed()
                await data_service.update_record_status(
                    self.row_index, "completed",
                    f"Survey completed at {self.user_answers.completed_at}"
                )

                conclusion = self.survey.conclusion if self.survey.conclusion else "Thank you for completing our survey!"

                # End the call after a brief delay
                await asyncio.sleep(3)
                await self.job_context.api.room.delete_room(DeleteRoomRequest(
                    room=self.job_context.room.name
                ))

                return conclusion + " [Call ended]"

            else:
                # Get next question
                next_question = await survey_service.get_next_question(self.survey_name, participant_id)

                if next_question:
                    self.current_question = next_question

                    # Reset email collection state for new question
                    if next_question.type == "email":
                        self.email_collection_state = "none"
                        self.email_component.reset()

                    self._update_instructions()

                    return f"Thank you. Now, {next_question.text}"
                else:
                    # No more questions (shouldn't happen if is_complete logic is correct)
                    await data_service.update_record_status(
                        self.row_index, "completed", "All questions answered"
                    )

                    await asyncio.sleep(3)
                    await self.job_context.api.room.delete_room(DeleteRoomRequest(
                        room=self.job_context.room.name
                    ))

                    return "Thank you for your time! [Call ended]"

        except Exception as e:
            logger.error(f"Error proceeding to next question: {e}")
            return "Thank you for your time. [Call ended]"

    @function_tool
    async def record_survey_answer(self, context: RunContext, answer: str):
        """Record the user's answer and handle survey progression (for non-email questions)"""
        logger.info(f"Survey answer recorded: {answer}")
        logger.info(f"Question ID: {self.current_question.id if self.current_question else 'unknown'}")
        logger.info(f"Row index: {self.row_index}")

        if not self.current_question or not self.user_answers:
            logger.error("No current question or user session available")
            return None, "Sorry, there was an error recording your answer."

        # Check if this is an email question - if so, redirect to email collection
        if self.current_question.type == "email":
            return await self.collect_email_address(context, answer)

        try:
            # Record the answer in the survey service
            participant_id = f"phone_{self.phone_number}_{self.row_index}"
            await survey_service.record_answer(
                self.survey_name, participant_id, self.current_question.id, answer
            )

            # Update CSV with the answer using data service
            await data_service.update_record_answer(self.row_index, answer)

            # Use the common progression logic
            next_response = await self._proceed_to_next_question()

            if "[Call ended]" in next_response:
                return None, next_response
            else:
                return None, f"Thank you for that answer. {next_response}"

        except Exception as e:
            logger.error(f"Error recording survey answer: {e}")
            return None, "Sorry, there was an error recording your answer. Could you please repeat it?"

async def entrypoint(ctx: JobContext):
    await ctx.connect()

    metadata_json = ctx.job.metadata
    logger.info(f"Received metadata: {metadata_json}")

    metadata = json.loads(metadata_json)
    phone_number = metadata.get("phone_number", "unknown")
    row_index = metadata.get("row_index", 1)

    # Get survey name from metadata, default to simple_preference
    survey_name = metadata.get("survey_name", "simple_preference")

    # Legacy support: if 'question' is provided but no survey_name, use simple_preference
    if "question" in metadata and "survey_name" not in metadata:
        logger.info("Legacy question format detected, using simple_preference survey")
        survey_name = "simple_preference"

    logger.info(f"Parsed metadata - phone_number: {phone_number}, row_index: {row_index}, survey_name: {survey_name}")

    context = {
        "phone_number": phone_number,
        "row_index": row_index
    }

    # Create and initialize the survey agent
    agent = SurveyAgent(survey_name=survey_name, context=context, job_context=ctx)

    # Initialize the survey
    if not await agent.initialize_survey():
        logger.error(f"Failed to initialize survey '{survey_name}' for call {row_index}")
        return

    session = AgentSession()

    await session.start(
        agent=agent,
        room=ctx.room
    )

if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, agent_name="survey-agent"))