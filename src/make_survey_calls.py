import asyncio
import logging
import os
import argparse
from pathlib import Path
from dotenv import load_dotenv
from services.call_queue_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CallItem, CallStatus
from services.call_queue_data_service import CSVCallQueueDataService
from services.survey_service import SurveyService

load_dotenv(dotenv_path=Path(__file__).parent.parent.parent / '.env')

logger = logging.getLogger("make-survey-calls")
logger.setLevel(logging.INFO)

# Configuration
room_name_prefix = "survey-call-"
agent_name = "survey-agent"
outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
csv_file_path = Path(__file__).parent / "survey_data.csv"

# Initialize services
data_service = CSVCallQueueDataService(csv_file_path)
survey_service = SurveyService()

async def survey_status_callback(call_item: CallItem, status: CallStatus, details: str):
    """Callback function to update <PERSON><PERSON> when call status changes"""
    await data_service.update_call_item_status(call_item, status, details)



async def process_survey_calls(survey_name: str = "simple_preference"):
    """Process all the survey calls using CallQueueHandler and CallQueueDataService"""
    # Validate survey exists
    survey = await survey_service.load_survey(survey_name)
    if not survey:
        logger.error(f"Survey '{survey_name}' not found. Available surveys: {await survey_service.list_available_surveys()}")
        return

    logger.info(f"Using survey: {survey.title}")

    # Load pending calls using the data service
    pending_call_items = await data_service.load_pending_call_items()

    logger.info(f"Found {len(pending_call_items)} pending survey calls to make")

    if not pending_call_items:
        logger.info("No pending calls to process")
        return

    # Add survey name to call metadata
    for call_item in pending_call_items:
        call_item.metadata['survey_name'] = survey_name

    # Create call queue handler with data service status callback
    call_handler = CallQueueHandler(
        agent_name=agent_name,
        sip_trunk_id=outbound_trunk_id,
        room_name_prefix=room_name_prefix,
        status_callback=survey_status_callback,
        monitor_timeout=180
    )

    # Process all calls (sequential by default, can be made concurrent)
    await call_handler.process_call_queue(pending_call_items, max_concurrent=1)

async def main():
    parser = argparse.ArgumentParser(description="Make survey calls using specified survey")
    parser.add_argument(
        "--survey",
        default="simple_preference",
        help="Name of the survey to use (default: simple_preference)"
    )
    parser.add_argument(
        "--list-surveys",
        action="store_true",
        help="List available surveys and exit"
    )

    args = parser.parse_args()

    if args.list_surveys:
        available_surveys = await survey_service.list_available_surveys()
        logger.info("Available surveys:")
        for survey_name in available_surveys:
            survey = await survey_service.load_survey(survey_name)
            if survey:
                logger.info(f"  - {survey_name}: {survey.title}")
                logger.info(f"    Description: {survey.description}")
                logger.info(f"    Questions: {len(survey.questions)}")
        return

    logger.info("Starting survey calls process")
    if not outbound_trunk_id:
        logger.error("SIP_OUTBOUND_TRUNK_ID is not set. Please add it to your .env file.")
        return

    await process_survey_calls(args.survey)
    logger.info("Survey calls process completed")

if __name__ == "__main__":
    asyncio.run(main())
