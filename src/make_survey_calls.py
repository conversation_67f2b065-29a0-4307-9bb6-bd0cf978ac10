import asyncio
import csv
import logging
import os
from pathlib import Path
from dotenv import load_dotenv
from datetime import datetime
from services.call_queue_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CallItem, CallStatus

load_dotenv(dotenv_path=Path(__file__).parent.parent.parent / '.env')

logger = logging.getLogger("make-survey-calls")
logger.setLevel(logging.INFO)

# Configuration
room_name_prefix = "survey-call-"
agent_name = "survey-agent"
outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
csv_file_path = Path(__file__).parent / "survey_data.csv"

async def survey_status_callback(call_item: CallItem, status: CallStatus, details: str):
    """Callback function to update CSV when call status changes"""
    # Extract row_index from call metadata
    row_index = call_item.metadata.get('row_index')
    if row_index:
        await update_csv_status(row_index, status.value, details)

async def update_csv_status(row_index, status, details=""):
    """Update the status column in the CSV file for a specific row"""
    try:
        # Read current data
        data = []
        with open(csv_file_path, 'r', newline='') as f:
            reader = csv.reader(f)
            headers = next(reader)
            for row in reader:
                data.append(row)

        # Ensure we have enough columns (phone, question, answer, status, details)
        for i, row in enumerate(data):
            while len(row) < 5:
                row.append('')

            # Update the specific row (row_index is 1-based)
            if i + 1 == row_index:
                row[3] = status  # Status column
                row[4] = details  # Details column

        # Write back to CSV
        with open(csv_file_path, 'w', newline='') as f:
            writer = csv.writer(f)
            writer.writerow(['phone_number', 'question', 'answer', 'status', 'details'])
            writer.writerows(data)

        logger.info(f"Updated CSV row {row_index} with status: {status}")
    except Exception as e:
        logger.error(f"Failed to update CSV status for row {row_index}: {e}")



async def read_csv_data():
    """Read the CSV file and return the data"""
    data = []
    with open(csv_file_path, 'r', newline='') as f:
        reader = csv.reader(f)
        headers = next(reader)  # Skip headers
        for i, row in enumerate(reader):
            if len(row) >= 2:
                data.append({
                    'row_index': i + 1,
                    'phone_number': row[0],
                    'question': row[1],
                    'answer': row[2] if len(row) > 2 else '',
                    'status': row[3] if len(row) > 3 else '',
                    'details': row[4] if len(row) > 4 else ''
                })

    return data

async def process_survey_calls():
    """Process all the survey calls in the CSV using CallQueueHandler"""
    # Read the CSV data
    data = await read_csv_data()

    logger.info(f"Found {len(data)} survey calls to make")

    # Filter out calls that already have answers or status
    pending_calls = []
    for item in data:
        if item['answer'] or (item['status'] and item['status'] != ''):
            logger.info(f"Skipping row {item['row_index']} as it already has an answer or status")
            continue

        # Create CallItem for each pending call
        call_item = CallItem(
            phone_number=item['phone_number'],
            metadata={
                'question': item['question'],
                'row_index': item['row_index']
            },
            call_id=str(item['row_index'])
        )
        pending_calls.append(call_item)

    if not pending_calls:
        logger.info("No pending calls to process")
        return

    logger.info(f"Processing {len(pending_calls)} pending survey calls")

    # Create call queue handler with CSV status callback
    call_handler = CallQueueHandler(
        agent_name=agent_name,
        sip_trunk_id=outbound_trunk_id,
        room_name_prefix=room_name_prefix,
        status_callback=survey_status_callback,
        monitor_timeout=180
    )

    # Process all calls (sequential by default, can be made concurrent)
    await call_handler.process_call_queue(pending_calls, max_concurrent=1)

async def main():
    logger.info("Starting survey calls process")
    if not outbound_trunk_id:
        logger.error("SIP_OUTBOUND_TRUNK_ID is not set. Please add it to your .env file.")
        return
    await process_survey_calls()
    logger.info("Survey calls process completed")

if __name__ == "__main__":
    asyncio.run(main())
