import asyncio
import logging
import os
from pathlib import Path
from dotenv import load_dotenv
from services.call_queue_handler import Call<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CallItem, CallStatus
from services.call_queue_data_service import CSVCallQueueDataService

load_dotenv(dotenv_path=Path(__file__).parent.parent.parent / '.env')

logger = logging.getLogger("make-survey-calls")
logger.setLevel(logging.INFO)

# Configuration
room_name_prefix = "survey-call-"
agent_name = "survey-agent"
outbound_trunk_id = os.getenv("SIP_OUTBOUND_TRUNK_ID")
csv_file_path = Path(__file__).parent / "survey_data.csv"

# Initialize data service
data_service = CSVCallQueueDataService(csv_file_path)

async def survey_status_callback(call_item: CallItem, status: CallStatus, details: str):
    """Callback function to update CSV when call status changes"""
    await data_service.update_call_item_status(call_item, status, details)



async def process_survey_calls():
    """Process all the survey calls using CallQueueHandler and CallQueueDataService"""
    # Load pending calls using the data service
    pending_call_items = await data_service.load_pending_call_items()

    logger.info(f"Found {len(pending_call_items)} pending survey calls to make")

    if not pending_call_items:
        logger.info("No pending calls to process")
        return

    # Create call queue handler with data service status callback
    call_handler = CallQueueHandler(
        agent_name=agent_name,
        sip_trunk_id=outbound_trunk_id,
        room_name_prefix=room_name_prefix,
        status_callback=survey_status_callback,
        monitor_timeout=180
    )

    # Process all calls (sequential by default, can be made concurrent)
    await call_handler.process_call_queue(pending_call_items, max_concurrent=1)

async def main():
    logger.info("Starting survey calls process")
    if not outbound_trunk_id:
        logger.error("SIP_OUTBOUND_TRUNK_ID is not set. Please add it to your .env file.")
        return
    await process_survey_calls()
    logger.info("Survey calls process completed")

if __name__ == "__main__":
    asyncio.run(main())
