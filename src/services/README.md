# Services Package

This package contains service classes that abstract LiveKit API interactions and provide clean interfaces for common operations.

## RoomsApiService

The `RoomsApiService` class provides a clean abstraction over LiveKit's Rooms API, specifically designed for monitoring call status and managing SIP participants.

### Features

- **Room Management**: List, create, and delete rooms
- **Participant Monitoring**: Get participant information and track SIP call status
- **Call Status Tracking**: Monitor call progression (dialing → ringing → active → hangup)
- **Automatic Cleanup**: Clean up completed call rooms
- **Context Manager Support**: Automatic resource management with async context managers

### Usage Examples

#### Basic Usage

```python
from services.rooms_api_service import RoomsApiService

# Using as context manager (recommended)
async with RoomsApiService() as rooms_service:
    # Get all survey call rooms
    calls = await rooms_service.get_calls_by_pattern("survey-call-")
    
    for call in calls:
        print(f"Room: {call.room_name}, Status: {call.call_status}")
```

#### Monitor Call Status

```python
async with RoomsApiService() as rooms_service:
    # Monitor a specific room for status changes
    status_changes = await rooms_service.monitor_room_status(
        room_name="survey-call-123",
        timeout_seconds=180
    )
    
    for timestamp, status in status_changes:
        print(f"Status changed to: {status}")
```

#### Create SIP Call

```python
async with RoomsApiService() as rooms_service:
    # Create agent dispatch
    dispatch = await rooms_service.create_agent_dispatch(
        agent_name="survey-agent",
        room_name="survey-call-123",
        metadata='{"phone": "+1234567890"}'
    )
    
    # Create SIP participant
    participant = await rooms_service.create_sip_participant(
        room_name="survey-call-123",
        sip_trunk_id="ST_xxxxx",
        phone_number="+1234567890"
    )
```

#### Cleanup Completed Calls

```python
async with RoomsApiService() as rooms_service:
    # Clean up all completed survey calls
    cleaned_count = await rooms_service.cleanup_rooms_by_status(
        room_name_pattern="survey-call-",
        cleanup_statuses=['hangup', 'completed']
    )
    print(f"Cleaned up {cleaned_count} rooms")
```

### Data Classes

#### RoomInfo
- `name`: Room name
- `creation_time`: Unix timestamp of room creation
- `num_participants`: Number of participants in room
- `metadata`: Room metadata string

#### ParticipantInfo
- `identity`: Participant identity
- `name`: Participant display name
- `joined_at`: Unix timestamp when participant joined
- `attributes`: Dictionary of participant attributes
- `call_status`: SIP call status (for phone participants)

#### CallInfo
- `room_name`: Name of the room
- `room_creation_time`: When the room was created
- `num_participants`: Number of participants
- `participants`: List of ParticipantInfo objects
- `call_status`: Overall call status
- `phone_participant`: The phone participant (if any)

### Call Status Values

The service tracks these SIP call status values:

- `dialing`: Call is being initiated
- `ringing`: Phone is ringing
- `active`: Call is answered and active
- `hangup`: Call has ended
- `unknown`: Status is not available

### Integration with Main Agent

The service is integrated into the main agent (`src/main.py`) to provide detailed logging of room and call status when conversations start.

### Command Line Tools

Use the example script to interact with the service:

```bash
# Show all survey calls
python src/examples/call_status_example.py --survey-calls

# Monitor a specific room
python src/examples/call_status_example.py --monitor survey-call-123 --duration 120

# Clean up completed calls
python src/examples/call_status_example.py --cleanup

# Get details for a specific room
python src/examples/call_status_example.py --room-details survey-call-123
```

### Error Handling

The service includes comprehensive error handling and logging. All methods will log errors and either return empty results or raise exceptions as appropriate.

### Resource Management

Always use the service as an async context manager to ensure proper cleanup of LiveKit API connections:

```python
# Good
async with RoomsApiService() as service:
    result = await service.some_method()

# Avoid (manual cleanup required)
service = RoomsApiService()
result = await service.some_method()
await service.close()  # Don't forget this!
```
