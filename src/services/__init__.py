"""Services package for AdvAgency"""

# Import call_queue_handler first (no external dependencies)
from .call_queue_handler import <PERSON>QueueHandler, CallItem, CallStatus

# Try to import rooms_api_service (requires livekit)
try:
    from .rooms_api_service import RoomsApiService, RoomInfo, ParticipantInfo, CallInfo
    __all__ = ['RoomsApiService', 'RoomInfo', 'ParticipantInfo', 'CallInfo', 'CallQueueHandler', 'CallItem', 'CallStatus']
except ImportError:
    # If livekit is not available, only export call_queue_handler classes
    __all__ = ['CallQueueHandler', 'CallItem', 'CallStatus']
