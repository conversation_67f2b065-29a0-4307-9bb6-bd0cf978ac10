"""Services package for AdvAgency"""

# Import call_queue_handler, data service, survey service, and email component (no external dependencies)
from .call_queue_handler import Call<PERSON>ueueHand<PERSON>, CallItem, CallStatus
from .call_queue_data_service import CallQueueDataService, CSVCallQueueDataService, CallRecord
from .survey_service import SurveyService, SurveyDefinition, SurveyQuestion, UserAnswers, UserAnswer
from .email_component import EmailCollectionComponent, EmailValidator, validate_email, spell_out_email

# Try to import rooms_api_service (requires livekit)
try:
    from .rooms_api_service import RoomsApiService, RoomInfo, ParticipantInfo, CallInfo
    __all__ = [
        'RoomsApiService', 'RoomInfo', 'ParticipantInfo', 'CallInfo',
        'CallQueueHandler', 'CallItem', 'CallStatus',
        'CallQueueDataService', 'CSVCallQueueDataService', 'CallRecord',
        'SurveyService', 'SurveyDefinition', 'SurveyQuestion', 'UserAnswers', 'UserAnswer',
        'EmailCollectionComponent', 'EmailValidator', 'validate_email', 'spell_out_email'
    ]
except ImportError:
    # If livekit is not available, only export call_queue_handler, data service, and survey service classes
    __all__ = [
        'CallQueueHandler', 'CallItem', 'CallStatus',
        'CallQueueDataService', 'CSVCallQueueDataService', 'CallRecord',
        'SurveyService', 'SurveyDefinition', 'SurveyQuestion', 'UserAnswers', 'UserAnswer',
        'EmailCollectionComponent', 'EmailValidator', 'validate_email', 'spell_out_email'
    ]
