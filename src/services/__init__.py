"""Services package for AdvAgency"""

# Import call_queue_handler, data service, and survey service (no external dependencies)
from .call_queue_handler import Call<PERSON>ueue<PERSON><PERSON><PERSON>, CallItem, CallStatus
from .call_queue_data_service import CallQueueDataService, CSVCallQueueDataService, CallRecord
from .survey_service import SurveyService, SurveyDefinition, SurveyQuestion, UserAnswers, UserAnswer

# Try to import rooms_api_service (requires livekit)
try:
    from .rooms_api_service import RoomsApiService, RoomInfo, ParticipantInfo, CallInfo
    __all__ = [
        'RoomsApiService', 'RoomInfo', 'ParticipantInfo', 'CallInfo',
        'CallQueueHandler', 'CallItem', 'CallStatus',
        'CallQueueDataService', 'CSVCallQueueDataService', 'CallRecord',
        'SurveyService', 'SurveyDefinition', 'SurveyQuestion', 'UserAnswers', 'UserAnswer'
    ]
except ImportError:
    # If livekit is not available, only export call_queue_handler, data service, and survey service classes
    __all__ = [
        'CallQueueHandler', 'CallItem', 'CallStatus',
        'CallQueueDataService', 'CSVCallQueueDataService', 'CallRecord',
        'SurveyService', 'SurveyDefinition', 'SurveyQuestion', 'UserAnswers', 'UserAnswer'
    ]
