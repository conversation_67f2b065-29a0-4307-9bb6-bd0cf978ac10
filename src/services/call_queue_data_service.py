import asyncio
import csv
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

from .call_queue_handler import CallItem, CallStatus

logger = logging.getLogger(__name__)


@dataclass
class CallRecord:
    """Represents a call record in the data store"""
    row_index: int
    phone_number: str
    question: str
    answer: str = ""
    status: str = ""
    details: str = ""
    
    def to_call_item(self) -> CallItem:
        """Convert to CallItem for queue processing"""
        return CallItem(
            phone_number=self.phone_number,
            metadata={
                'question': self.question,
                'row_index': self.row_index,
                'answer': self.answer
            },
            call_id=str(self.row_index),
            status=CallStatus(self.status) if self.status and self.status in [s.value for s in CallStatus] else CallStatus.PENDING,
            details=self.details
        )
    
    @classmethod
    def from_call_item(cls, call_item: CallItem) -> 'CallRecord':
        """Create CallRecord from CallItem"""
        return cls(
            row_index=call_item.metadata.get('row_index', 0),
            phone_number=call_item.phone_number,
            question=call_item.metadata.get('question', ''),
            answer=call_item.metadata.get('answer', ''),
            status=call_item.status.value,
            details=call_item.details
        )


class CallQueueDataService(ABC):
    """Abstract base class for call queue data services"""
    
    @abstractmethod
    async def load_all_records(self) -> List[CallRecord]:
        """Load all call records from the data source"""
        pass
    
    @abstractmethod
    async def load_pending_records(self) -> List[CallRecord]:
        """Load only pending call records (no answer or status)"""
        pass
    
    @abstractmethod
    async def update_record_status(self, row_index: int, status: str, details: str = "") -> bool:
        """Update the status of a specific record"""
        pass
    
    @abstractmethod
    async def update_record_answer(self, row_index: int, answer: str) -> bool:
        """Update the answer for a specific record"""
        pass
    
    @abstractmethod
    async def get_record_by_index(self, row_index: int) -> Optional[CallRecord]:
        """Get a specific record by its index"""
        pass
    
    async def load_pending_call_items(self) -> List[CallItem]:
        """Load pending records as CallItem objects"""
        records = await self.load_pending_records()
        return [record.to_call_item() for record in records]
    
    async def update_call_item_status(self, call_item: CallItem, status: CallStatus, details: str = "") -> bool:
        """Update status using CallItem and CallStatus"""
        row_index = call_item.metadata.get('row_index')
        if row_index:
            return await self.update_record_status(row_index, status.value, details)
        return False


class CSVCallQueueDataService(CallQueueDataService):
    """CSV-based implementation of CallQueueDataService"""
    
    def __init__(self, csv_file_path: Union[str, Path]):
        """
        Initialize CSV data service.
        
        Args:
            csv_file_path: Path to the CSV file
        """
        self.csv_file_path = Path(csv_file_path)
        self.headers = ['phone_number', 'question', 'answer', 'status', 'details']
        self._lock = asyncio.Lock()  # Prevent concurrent CSV access
    
    async def _ensure_csv_structure(self):
        """Ensure CSV file exists with proper headers"""
        if not self.csv_file_path.exists():
            logger.info(f"Creating new CSV file: {self.csv_file_path}")
            async with self._lock:
                with open(self.csv_file_path, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['phone_number', 'question', 'answer', 'status', 'details'])
    
    async def _read_csv_data(self) -> List[List[str]]:
        """Read raw CSV data"""
        await self._ensure_csv_structure()
        
        data = []
        try:
            with open(self.csv_file_path, 'r', newline='') as f:
                reader = csv.reader(f)
                headers = next(reader, None)  # Skip headers
                for row in reader:
                    # Ensure row has enough columns
                    while len(row) < 5:
                        row.append('')
                    data.append(row)
        except FileNotFoundError:
            logger.warning(f"CSV file not found: {self.csv_file_path}")
        except Exception as e:
            logger.error(f"Error reading CSV file: {e}")
            raise
        
        return data
    
    async def _write_csv_data(self, data: List[List[str]]):
        """Write raw CSV data"""
        async with self._lock:
            try:
                with open(self.csv_file_path, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerow(['phone_number', 'question', 'answer', 'status', 'details'])
                    writer.writerows(data)
                logger.debug(f"Successfully wrote {len(data)} rows to CSV")
            except Exception as e:
                logger.error(f"Error writing CSV file: {e}")
                raise
    
    async def load_all_records(self) -> List[CallRecord]:
        """Load all call records from CSV"""
        data = await self._read_csv_data()
        records = []
        
        for i, row in enumerate(data):
            if len(row) >= 2 and row[0] and row[1]:  # Must have phone and question
                records.append(CallRecord(
                    row_index=i + 1,
                    phone_number=row[0],
                    question=row[1],
                    answer=row[2] if len(row) > 2 else '',
                    status=row[3] if len(row) > 3 else '',
                    details=row[4] if len(row) > 4 else ''
                ))
        
        logger.info(f"Loaded {len(records)} records from CSV")
        return records
    
    async def load_pending_records(self) -> List[CallRecord]:
        """Load only pending call records"""
        all_records = await self.load_all_records()
        pending_records = []
        
        for record in all_records:
            # Consider pending if no answer and no status (or empty status)
            if not record.answer and (not record.status or record.status.strip() == ''):
                pending_records.append(record)
        
        logger.info(f"Found {len(pending_records)} pending records out of {len(all_records)} total")
        return pending_records
    
    async def update_record_status(self, row_index: int, status: str, details: str = "") -> bool:
        """Update the status of a specific record"""
        try:
            data = await self._read_csv_data()
            
            # Find and update the specific row (row_index is 1-based)
            updated = False
            for i, row in enumerate(data):
                if i + 1 == row_index:
                    # Ensure row has enough columns
                    while len(row) < 5:
                        row.append('')
                    
                    row[3] = status  # Status column
                    row[4] = details  # Details column
                    updated = True
                    break
            
            if updated:
                await self._write_csv_data(data)
                logger.info(f"Updated CSV row {row_index} with status: {status}")
                return True
            else:
                logger.warning(f"Row {row_index} not found for status update")
                return False
                
        except Exception as e:
            logger.error(f"Failed to update CSV status for row {row_index}: {e}")
            return False
    
    async def update_record_answer(self, row_index: int, answer: str) -> bool:
        """Update the answer for a specific record"""
        try:
            data = await self._read_csv_data()
            
            # Find and update the specific row
            updated = False
            for i, row in enumerate(data):
                if i + 1 == row_index:
                    # Ensure row has enough columns
                    while len(row) < 5:
                        row.append('')
                    
                    row[2] = answer  # Answer column
                    row[3] = 'completed'  # Status column
                    row[4] = f"Answer recorded at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"  # Details
                    updated = True
                    break
            
            if updated:
                await self._write_csv_data(data)
                logger.info(f"Updated CSV row {row_index} with answer: {answer}")
                return True
            else:
                logger.warning(f"Row {row_index} not found for answer update")
                return False
                
        except Exception as e:
            logger.error(f"Failed to update CSV answer for row {row_index}: {e}")
            return False
    
    async def get_record_by_index(self, row_index: int) -> Optional[CallRecord]:
        """Get a specific record by its index"""
        all_records = await self.load_all_records()
        
        for record in all_records:
            if record.row_index == row_index:
                return record
        
        return None
    
    async def add_record(self, phone_number: str, question: str) -> int:
        """Add a new record and return its row index"""
        data = await self._read_csv_data()
        
        # Add new row
        new_row = [phone_number, question, '', '', '']
        data.append(new_row)
        
        await self._write_csv_data(data)
        
        new_row_index = len(data)
        logger.info(f"Added new record at row {new_row_index}: {phone_number}")
        return new_row_index
    
    async def get_summary_stats(self) -> Dict[str, int]:
        """Get summary statistics of the data"""
        records = await self.load_all_records()
        
        stats = {
            'total': len(records),
            'pending': 0,
            'dialing': 0,
            'ringing': 0,
            'active': 0,
            'completed': 0,
            'timeout': 0,
            'error': 0
        }
        
        for record in records:
            status = record.status.lower() if record.status else 'pending'
            if status in stats:
                stats[status] += 1
            elif not record.answer and not record.status:
                stats['pending'] += 1
            else:
                # Unknown status, count as pending
                stats['pending'] += 1
        
        return stats
