import asyncio
import json
import logging
import time
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union

# Import RoomsApiService conditionally to avoid dependency issues during testing
try:
    from .rooms_api_service import RoomsApiService
except ImportError:
    # If livekit is not available, we'll handle this in the methods that use it
    RoomsApiService = None

logger = logging.getLogger(__name__)


class CallStatus(Enum):
    """Enumeration of possible call statuses"""
    PENDING = "pending"
    DIALING = "dialing"
    RINGING = "ringing"
    ACTIVE = "active"
    COMPLETED = "completed"
    TIMEOUT = "timeout"
    ERROR = "error"


@dataclass
class CallItem:
    """Represents a single call to be made"""
    phone_number: str
    metadata: Dict[str, Any]
    call_id: str
    status: CallStatus = CallStatus.PENDING
    details: str = ""
    
    def to_json_metadata(self) -> str:
        """Convert metadata to JSON string for agent dispatch"""
        return json.dumps({
            "phone_number": self.phone_number,
            "call_id": self.call_id,
            **self.metadata
        })


class CallQueueHandler:
    """
    Handles a queue of calls without caring about CSV files or surveys.
    Provides callbacks for status updates so the caller can decide what to do with them.
    """
    
    def __init__(
        self,
        agent_name: str,
        sip_trunk_id: str,
        room_name_prefix: str = "call-",
        status_callback: Optional[Callable[[CallItem, CallStatus, str], None]] = None,
        monitor_timeout: int = 180
    ):
        """
        Initialize the call queue handler.
        
        Args:
            agent_name: Name of the agent to dispatch
            sip_trunk_id: SIP trunk ID for outbound calls
            room_name_prefix: Prefix for room names
            status_callback: Optional callback function for status updates
            monitor_timeout: Timeout in seconds for call monitoring
        """
        self.agent_name = agent_name
        self.sip_trunk_id = sip_trunk_id
        self.room_name_prefix = room_name_prefix
        self.status_callback = status_callback
        self.monitor_timeout = monitor_timeout
        self._active_calls: Dict[str, CallItem] = {}
        self._monitoring_tasks: Dict[str, asyncio.Task] = {}
    
    def _get_room_name(self, call_id: str) -> str:
        """Generate room name for a call"""
        return f"{self.room_name_prefix}{call_id}"
    
    async def _update_call_status(self, call_item: CallItem, status: CallStatus, details: str = ""):
        """Update call status and trigger callback if provided"""
        call_item.status = status
        call_item.details = details
        
        logger.info(f"Call {call_item.call_id} status updated: {status.value} - {details}")
        
        if self.status_callback:
            try:
                # Handle both sync and async callbacks
                result = self.status_callback(call_item, status, details)
                if asyncio.iscoroutine(result):
                    await result
            except Exception as e:
                logger.error(f"Error in status callback for call {call_item.call_id}: {e}")
    
    async def make_call(self, call_item: CallItem) -> bool:
        """
        Initiate a single call.

        Args:
            call_item: The call to make

        Returns:
            True if call was successfully initiated, False otherwise
        """
        if RoomsApiService is None:
            raise ImportError("RoomsApiService is not available. Please install livekit dependencies.")

        room_name = self._get_room_name(call_item.call_id)

        try:
            # Store the call item
            self._active_calls[call_item.call_id] = call_item

            async with RoomsApiService() as rooms_service:
                logger.info(f"Creating dispatch for agent {self.agent_name} in room {room_name}")
                
                # Create agent dispatch
                dispatch = await rooms_service.create_agent_dispatch(
                    agent_name=self.agent_name,
                    room_name=room_name,
                    metadata=call_item.to_json_metadata()
                )
                logger.info(f"Created dispatch: {dispatch}")
                
                # Update status to dialing
                await self._update_call_status(
                    call_item, 
                    CallStatus.DIALING, 
                    f"Call initiated at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                )
                
                logger.info(f"Dialing {call_item.phone_number} to room {room_name}")
                
                # Create SIP participant
                sip_participant = await rooms_service.create_sip_participant(
                    room_name=room_name,
                    sip_trunk_id=self.sip_trunk_id,
                    phone_number=call_item.phone_number,
                    participant_identity="phone_user"
                )
                logger.info(f"Created SIP participant: {sip_participant}")
            
            # Start monitoring the call status
            monitor_task = asyncio.create_task(self._monitor_call_status(call_item))
            self._monitoring_tasks[call_item.call_id] = monitor_task
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to make call {call_item.call_id}: {e}")
            await self._update_call_status(call_item, CallStatus.ERROR, f"Failed to initiate call: {str(e)}")
            return False
    
    async def _monitor_call_status(self, call_item: CallItem):
        """Monitor the call status for a specific call"""
        if RoomsApiService is None:
            logger.error("RoomsApiService is not available for monitoring")
            await self._update_call_status(call_item, CallStatus.ERROR, "RoomsApiService not available")
            return

        room_name = self._get_room_name(call_item.call_id)

        async with RoomsApiService() as rooms_service:
            try:
                start_time = time.time()
                last_status = "dialing"
                
                while time.time() - start_time < self.monitor_timeout:
                    try:
                        # Get call information
                        call_info = await rooms_service.get_call_info(room_name)
                        
                        if call_info and call_info.phone_participant:
                            call_status = call_info.call_status or 'unknown'
                            
                            if call_status != last_status:
                                logger.info(f"Call status changed for {call_item.phone_number}: {last_status} -> {call_status}")
                                
                                if call_status == "ringing":
                                    await self._update_call_status(
                                        call_item, 
                                        CallStatus.RINGING, 
                                        f"Phone ringing at {datetime.now().strftime('%H:%M:%S')}"
                                    )
                                elif call_status == "active":
                                    await self._update_call_status(
                                        call_item, 
                                        CallStatus.ACTIVE, 
                                        f"Call answered at {datetime.now().strftime('%H:%M:%S')}"
                                    )
                                elif call_status == "hangup":
                                    await self._update_call_status(
                                        call_item, 
                                        CallStatus.COMPLETED, 
                                        f"Call ended at {datetime.now().strftime('%H:%M:%S')}"
                                    )
                                    break
                                
                                last_status = call_status
                        
                        # Wait before next check
                        await asyncio.sleep(2)
                        
                    except Exception as e:
                        logger.error(f"Error checking call status for {room_name}: {e}")
                        await asyncio.sleep(5)
                
                # If we timeout without seeing hangup, mark as timeout
                if last_status not in ["hangup", "completed"]:
                    await self._update_call_status(
                        call_item, 
                        CallStatus.TIMEOUT, 
                        f"Call monitoring timeout after {self.monitor_timeout}s"
                    )
                    
            except Exception as e:
                logger.error(f"Error in call monitoring for {room_name}: {e}")
                await self._update_call_status(
                    call_item, 
                    CallStatus.ERROR, 
                    f"Monitoring error: {str(e)}"
                )
            finally:
                # Clean up
                if call_item.call_id in self._active_calls:
                    del self._active_calls[call_item.call_id]
                if call_item.call_id in self._monitoring_tasks:
                    del self._monitoring_tasks[call_item.call_id]
    
    async def process_call_queue(self, call_items: List[CallItem], max_concurrent: int = 1) -> List[CallItem]:
        """
        Process a queue of calls with optional concurrency control.
        
        Args:
            call_items: List of calls to process
            max_concurrent: Maximum number of concurrent calls (default: 1)
            
        Returns:
            List of processed call items with updated statuses
        """
        logger.info(f"Processing {len(call_items)} calls with max concurrency: {max_concurrent}")
        
        # Create semaphore to limit concurrency
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_call(call_item: CallItem):
            async with semaphore:
                await self.make_call(call_item)
        
        # Start all calls
        tasks = [process_single_call(call_item) for call_item in call_items]
        await asyncio.gather(*tasks, return_exceptions=True)
        
        # Wait for all monitoring to complete
        if self._monitoring_tasks:
            await asyncio.gather(*self._monitoring_tasks.values(), return_exceptions=True)
        
        return call_items
    
    async def cancel_all_calls(self):
        """Cancel all active call monitoring tasks"""
        for task in self._monitoring_tasks.values():
            if not task.done():
                task.cancel()
        
        if self._monitoring_tasks:
            await asyncio.gather(*self._monitoring_tasks.values(), return_exceptions=True)
        
        self._monitoring_tasks.clear()
        self._active_calls.clear()
    
    def get_active_calls(self) -> Dict[str, CallItem]:
        """Get currently active calls"""
        return self._active_calls.copy()
