import re
import logging
from dataclasses import dataclass
from typing import Optional, Tuple, List, Dict
from enum import Enum

logger = logging.getLogger(__name__)


class EmailValidationResult(Enum):
    """Email validation results"""
    VALID = "valid"
    INVALID_FORMAT = "invalid_format"
    MISSING_AT = "missing_at"
    MISSING_DOMAIN = "missing_domain"
    INVALID_DOMAIN = "invalid_domain"
    TOO_LONG = "too_long"
    TOO_SHORT = "too_short"


@dataclass
class EmailCollectionInstructions:
    """Structured email collection instructions extracted from Richard prompt"""
    
    # Transition and request
    transition_request: str = (
        "Thank you for sharing all that. Based on what you've said, I think one of our partners "
        "could definitely offer some value as you move forward. To help them prepare and send you "
        "some relevant information beforehand, what would be the best email address for you? "
        "I'll just add it to your profile."
    )
    
    # Clarification requests
    clarification_requests: List[str] = None
    
    # Read-back confirmation
    readback_intro: str = (
        "Okay, thank you. Let me just read that back to make sure I have it correct. "
        "That's {spelled_email}. Is that correct?"
    )
    
    # Confirmation responses
    confirmation_success: str = "Perfect, thank you. I've got that down."
    
    correction_request: str = (
        "My apologies. Could you please spell it out for me one more time? "
        "I want to ensure our partner can reach you."
    )
    
    correction_success: str = "Great, got it this time. Thank you for clarifying."
    
    def __post_init__(self):
        if self.clarification_requests is None:
            self.clarification_requests = [
                "Could you please spell that out for me just to make sure I get it exactly right?",
                "Sorry, I missed that last part, could you repeat the domain for me?",
                "I think I missed that - could you spell out the part after the @ symbol?"
            ]


@dataclass
class EmailValidationResult:
    """Result of email validation"""
    is_valid: bool
    email: str
    formatted_email: str
    issues: List[str]
    suggestions: List[str]


class EmailValidator:
    """Email validation and formatting utilities"""
    
    def __init__(self):
        # Common email providers for recognition
        self.common_providers = {
            'gmail.com', 'outlook.com', 'hotmail.com', 'yahoo.com', 'icloud.com',
            'aol.com', 'live.com', 'msn.com', 'protonmail.com', 'zoho.com'
        }
        
        # Common typos and corrections
        self.common_typos = {
            'gmai.com': 'gmail.com',
            'gmial.com': 'gmail.com',
            'gmail.co': 'gmail.com',
            'outlok.com': 'outlook.com',
            'outloo.com': 'outlook.com',
            'yahooo.com': 'yahoo.com',
            'yaho.com': 'yahoo.com',
            'hotmial.com': 'hotmail.com'
        }
    
    def validate_email(self, email: str) -> EmailValidationResult:
        """Validate and format email address"""
        if not email:
            return EmailValidationResult(
                is_valid=False,
                email="",
                formatted_email="",
                issues=["Email is empty"],
                suggestions=["Please provide an email address"]
            )
        
        # Clean the email
        cleaned_email = email.strip().lower()
        issues = []
        suggestions = []
        
        # Basic format validation
        if len(cleaned_email) < 5:
            issues.append("Email too short")
            suggestions.append("Email should be at least 5 characters")
        
        if len(cleaned_email) > 254:
            issues.append("Email too long")
            suggestions.append("Email should be less than 254 characters")
        
        if '@' not in cleaned_email:
            issues.append("Missing @ symbol")
            suggestions.append("Email should contain @ symbol")
            return EmailValidationResult(
                is_valid=False,
                email=email,
                formatted_email=cleaned_email,
                issues=issues,
                suggestions=suggestions
            )
        
        # Split into local and domain parts
        parts = cleaned_email.split('@')
        if len(parts) != 2:
            issues.append("Invalid format - multiple @ symbols")
            suggestions.append("Email should have exactly one @ symbol")
        
        local_part, domain_part = parts[0], parts[1]
        
        # Validate local part
        if not local_part:
            issues.append("Missing username before @")
            suggestions.append("Email should have username before @")
        
        if len(local_part) > 64:
            issues.append("Username too long")
            suggestions.append("Username should be less than 64 characters")
        
        # Validate domain part
        if not domain_part:
            issues.append("Missing domain after @")
            suggestions.append("Email should have domain after @")
        
        if '.' not in domain_part:
            issues.append("Domain missing extension")
            suggestions.append("Domain should have extension (e.g., .com)")
        
        # Check for common typos
        if domain_part in self.common_typos:
            corrected_domain = self.common_typos[domain_part]
            suggestions.append(f"Did you mean {local_part}@{corrected_domain}?")
            cleaned_email = f"{local_part}@{corrected_domain}"
        
        # Advanced regex validation
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, cleaned_email):
            issues.append("Invalid email format")
            suggestions.append("Please check the email format")
        
        is_valid = len(issues) == 0
        
        return EmailValidationResult(
            is_valid=is_valid,
            email=email,
            formatted_email=cleaned_email,
            issues=issues,
            suggestions=suggestions
        )
    
    def spell_out_email(self, email: str) -> str:
        """Convert email to spelled-out format for verbal confirmation"""
        if not email:
            return ""
        
        # Clean the email
        cleaned_email = email.strip().lower()
        
        # Split into characters and handle special characters
        spelled_parts = []
        
        for char in cleaned_email:
            if char == '@':
                spelled_parts.append('at')
            elif char == '.':
                spelled_parts.append('dot')
            elif char == '-':
                spelled_parts.append('dash')
            elif char == '_':
                spelled_parts.append('underscore')
            elif char == '+':
                spelled_parts.append('plus')
            elif char.isalnum():
                spelled_parts.append(char.upper())
            else:
                spelled_parts.append(char)
        
        return ' '.join(spelled_parts)
    
    def format_for_readback(self, email: str) -> str:
        """Format email for verbal read-back confirmation"""
        if not email:
            return ""
        
        cleaned_email = email.strip().lower()
        
        # Check if it's a common provider
        if '@' in cleaned_email:
            local_part, domain_part = cleaned_email.split('@', 1)
            
            if domain_part in self.common_providers:
                # For common providers, can just say the provider name
                spelled_local = self.spell_out_email(local_part)
                return f"{spelled_local} at {domain_part}"
            else:
                # For uncommon domains, spell everything out
                return self.spell_out_email(cleaned_email)
        
        return self.spell_out_email(cleaned_email)


class EmailCollectionComponent:
    """Complete email collection component with instructions and validation"""
    
    def __init__(self):
        self.instructions = EmailCollectionInstructions()
        self.validator = EmailValidator()
        self.collection_attempts = 0
        self.max_attempts = 3
    
    def get_initial_request(self) -> str:
        """Get the initial email request message"""
        return self.instructions.transition_request
    
    def get_clarification_request(self, attempt: int = 0) -> str:
        """Get clarification request based on attempt number"""
        requests = self.instructions.clarification_requests
        if attempt < len(requests):
            return requests[attempt]
        return requests[-1]  # Use last request if we exceed available options
    
    def get_readback_confirmation(self, email: str) -> str:
        """Get read-back confirmation message with spelled-out email"""
        spelled_email = self.validator.format_for_readback(email)
        return self.instructions.readback_intro.format(spelled_email=spelled_email)
    
    def process_email_input(self, email_input: str) -> Dict:
        """Process email input and return validation results with next steps"""
        self.collection_attempts += 1
        
        # Validate the email
        validation = self.validator.validate_email(email_input)
        
        response = {
            'validation': validation,
            'attempt': self.collection_attempts,
            'max_attempts_reached': self.collection_attempts >= self.max_attempts,
            'next_action': None,
            'response_message': None
        }
        
        if validation.is_valid:
            # Email is valid, proceed to read-back confirmation
            response['next_action'] = 'readback_confirmation'
            response['response_message'] = self.get_readback_confirmation(validation.formatted_email)
        else:
            # Email has issues
            if self.collection_attempts >= self.max_attempts:
                response['next_action'] = 'skip_email'
                response['response_message'] = (
                    "I'm having trouble getting the email address clearly. "
                    "That's okay - our partner can reach out to you through other means. "
                    "Let's continue with the next question."
                )
            else:
                response['next_action'] = 'request_clarification'
                response['response_message'] = self.get_clarification_request(self.collection_attempts - 1)
        
        return response
    
    def handle_confirmation_response(self, confirmed: bool, email: str) -> Dict:
        """Handle user's confirmation of read-back email"""
        if confirmed:
            return {
                'success': True,
                'final_email': email,
                'response_message': self.instructions.confirmation_success
            }
        else:
            # User said email is incorrect, request correction
            if self.collection_attempts >= self.max_attempts:
                return {
                    'success': False,
                    'final_email': None,
                    'response_message': (
                        "I'm having trouble getting the email address right. "
                        "That's okay - our partner can follow up with you through other means."
                    )
                }
            else:
                return {
                    'success': False,
                    'final_email': None,
                    'response_message': self.instructions.correction_request,
                    'next_action': 'request_correction'
                }
    
    def reset(self):
        """Reset the component for a new email collection session"""
        self.collection_attempts = 0
    
    def get_collection_summary(self) -> Dict:
        """Get summary of the email collection process"""
        return {
            'instructions': {
                'initial_request': self.instructions.transition_request,
                'clarification_options': self.instructions.clarification_requests,
                'readback_template': self.instructions.readback_intro,
                'success_message': self.instructions.confirmation_success,
                'correction_request': self.instructions.correction_request
            },
            'validation_features': {
                'format_validation': True,
                'typo_correction': True,
                'common_providers': list(self.validator.common_providers),
                'spelling_support': True
            },
            'process_flow': [
                "1. Initial email request",
                "2. Listen for email input",
                "3. Validate email format",
                "4. Spell out email for confirmation",
                "5. Handle confirmation/correction",
                "6. Retry if needed (max 3 attempts)",
                "7. Skip if unsuccessful after max attempts"
            ]
        }


# Convenience function for quick email validation
def validate_email(email: str) -> EmailValidationResult:
    """Quick email validation function"""
    validator = EmailValidator()
    return validator.validate_email(email)


# Convenience function for email spelling
def spell_out_email(email: str) -> str:
    """Quick email spelling function"""
    validator = EmailValidator()
    return validator.spell_out_email(email)
