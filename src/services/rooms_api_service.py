import asyncio
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from livekit import api

logger = logging.getLogger(__name__)

@dataclass
class RoomInfo:
    """Information about a LiveKit room"""
    name: str
    creation_time: int
    num_participants: int
    metadata: str = ""

@dataclass
class ParticipantInfo:
    """Information about a room participant"""
    identity: str
    name: str
    joined_at: int
    attributes: Dict[str, str]
    call_status: Optional[str] = None

@dataclass
class CallInfo:
    """Complete call information combining room and participant data"""
    room_name: str
    room_creation_time: int
    num_participants: int
    participants: List[ParticipantInfo]
    call_status: Optional[str] = None
    phone_participant: Optional[ParticipantInfo] = None

class RoomsApiService:
    """Service for interacting with LiveKit Rooms API"""
    
    def __init__(self):
        self._api_instance = None
    
    async def _get_api(self) -> api.LiveKitAPI:
        """Get or create LiveKit API instance"""
        if self._api_instance is None:
            self._api_instance = api.LiveKitAPI()
        return self._api_instance
    
    async def close(self):
        """Close the API connection"""
        if self._api_instance:
            await self._api_instance.aclose()
            self._api_instance = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    async def list_rooms(self, name_filter: Optional[str] = None) -> List[RoomInfo]:
        """List all rooms, optionally filtered by name pattern"""
        lkapi = await self._get_api()
        
        try:
            rooms_response = await lkapi.room.list_rooms(api.ListRoomsRequest())
            rooms = []
            
            for room in rooms_response.rooms:
                if name_filter is None or room.name.startswith(name_filter):
                    rooms.append(RoomInfo(
                        name=room.name,
                        creation_time=room.creation_time,
                        num_participants=room.num_participants,
                        metadata=room.metadata
                    ))
            
            return rooms
            
        except Exception as e:
            logger.error(f"Error listing rooms: {e}")
            raise
    
    async def get_room_participants(self, room_name: str) -> List[ParticipantInfo]:
        """Get all participants in a specific room"""
        lkapi = await self._get_api()
        
        try:
            participants_response = await lkapi.room.list_participants(
                api.ListParticipantsRequest(room=room_name)
            )
            
            participants = []
            for participant in participants_response.participants:
                participant_info = ParticipantInfo(
                    identity=participant.identity,
                    name=participant.name,
                    joined_at=participant.joined_at,
                    attributes=dict(participant.attributes)
                )
                
                # Extract call status for SIP participants
                if participant.identity == "phone_user":
                    participant_info.call_status = participant.attributes.get('sip.callStatus', 'unknown')
                
                participants.append(participant_info)
            
            return participants
            
        except Exception as e:
            logger.error(f"Error getting participants for room {room_name}: {e}")
            raise
    
    async def get_call_info(self, room_name: str) -> Optional[CallInfo]:
        """Get complete call information for a room"""
        try:
            # Get room info
            rooms = await self.list_rooms(name_filter=room_name)
            room = next((r for r in rooms if r.name == room_name), None)
            
            if not room:
                return None
            
            # Get participants
            participants = await self.get_room_participants(room_name)
            
            # Find phone participant and extract call status
            phone_participant = next((p for p in participants if p.identity == "phone_user"), None)
            call_status = phone_participant.call_status if phone_participant else None
            
            return CallInfo(
                room_name=room.name,
                room_creation_time=room.creation_time,
                num_participants=room.num_participants,
                participants=participants,
                call_status=call_status,
                phone_participant=phone_participant
            )
            
        except Exception as e:
            logger.error(f"Error getting call info for room {room_name}: {e}")
            return None
    
    async def get_calls_by_pattern(self, room_name_pattern: str) -> List[CallInfo]:
        """Get call information for all rooms matching a pattern"""
        try:
            rooms = await self.list_rooms(name_filter=room_name_pattern)
            calls = []
            
            for room in rooms:
                call_info = await self.get_call_info(room.name)
                if call_info:
                    calls.append(call_info)
            
            return calls
            
        except Exception as e:
            logger.error(f"Error getting calls by pattern {room_name_pattern}: {e}")
            return []
    
    async def monitor_room_status(self, room_name: str, timeout_seconds: int = 180) -> List[Tuple[float, str]]:
        """Monitor a room's call status over time, returning status changes with timestamps"""
        import time
        
        status_changes = []
        start_time = time.time()
        last_status = None
        
        while time.time() - start_time < timeout_seconds:
            try:
                call_info = await self.get_call_info(room_name)
                
                if call_info and call_info.call_status != last_status:
                    timestamp = time.time()
                    status_changes.append((timestamp, call_info.call_status))
                    logger.info(f"Room {room_name} status changed: {last_status} -> {call_info.call_status}")
                    last_status = call_info.call_status
                    
                    # Break if call ended
                    if call_info.call_status in ['hangup', 'completed']:
                        break
                
                # Wait before next check
                await asyncio.sleep(2)
                
            except Exception as e:
                logger.error(f"Error monitoring room {room_name}: {e}")
                await asyncio.sleep(5)
        
        return status_changes
    
    async def delete_room(self, room_name: str) -> bool:
        """Delete a room"""
        lkapi = await self._get_api()
        
        try:
            await lkapi.room.delete_room(api.DeleteRoomRequest(room=room_name))
            logger.info(f"Deleted room: {room_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting room {room_name}: {e}")
            return False
    
    async def cleanup_rooms_by_status(self, room_name_pattern: str, 
                                    cleanup_statuses: List[str] = None) -> int:
        """Clean up rooms based on their call status"""
        if cleanup_statuses is None:
            cleanup_statuses = ['hangup', 'completed']
        
        calls = await self.get_calls_by_pattern(room_name_pattern)
        cleaned_count = 0
        
        for call in calls:
            should_cleanup = (
                call.call_status in cleanup_statuses or 
                call.num_participants == 0
            )
            
            if should_cleanup:
                if await self.delete_room(call.room_name):
                    cleaned_count += 1
        
        return cleaned_count
    
    async def create_sip_participant(self, room_name: str, sip_trunk_id: str, 
                                   phone_number: str, participant_identity: str = "phone_user"):
        """Create a SIP participant in a room"""
        lkapi = await self._get_api()
        
        try:
            sip_participant = await lkapi.sip.create_sip_participant(
                api.CreateSIPParticipantRequest(
                    room_name=room_name,
                    sip_trunk_id=sip_trunk_id,
                    sip_call_to=phone_number,
                    participant_identity=participant_identity,
                )
            )
            logger.info(f"Created SIP participant for {phone_number} in room {room_name}")
            return sip_participant
            
        except Exception as e:
            logger.error(f"Error creating SIP participant: {e}")
            raise
    
    async def create_agent_dispatch(self, agent_name: str, room_name: str, metadata: str = ""):
        """Create an agent dispatch for a room"""
        lkapi = await self._get_api()
        
        try:
            dispatch = await lkapi.agent_dispatch.create_dispatch(
                api.CreateAgentDispatchRequest(
                    agent_name=agent_name,
                    room=room_name,
                    metadata=metadata
                )
            )
            logger.info(f"Created agent dispatch for {agent_name} in room {room_name}")
            return dispatch
            
        except Exception as e:
            logger.error(f"Error creating agent dispatch: {e}")
            raise
