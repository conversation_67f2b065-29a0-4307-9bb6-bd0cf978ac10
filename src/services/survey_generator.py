import asyncio
import json
import logging
import re
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

# Try to import yaml, provide fallback if not available
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    yaml = None

logger = logging.getLogger(__name__)


@dataclass
class ExtractedQuestion:
    """Represents a question extracted from an LLM prompt"""
    text: str
    question_type: str = "text"
    options: List[str] = field(default_factory=list)
    required: bool = True
    category: str = ""
    follow_up: Optional[str] = None


class SurveyGenerator:
    """Generates survey YAML files from LLM prompts"""
    
    def __init__(self):
        self.question_patterns = [
            # Direct questions with quotes
            r'"([^"]*\?)"',
            r'"([^"]*\?[^"]*)"',
            
            # Questions with specific markers
            r'- "([^"]*\?)"',
            r'• "([^"]*\?)"',
            r'\* "([^"]*\?)"',
            
            # Questions without quotes but with question marks
            r'(?:Ask|Question|Q\d*[:.]\s*)([^.!]*\?)',
            
            # Yes/no questions
            r'([^.!]*\?\s*(?:Yes or no|Y/N))',
            
            # Multiple choice indicators
            r'([^.!]*\?[^.!]*(?:or|,)[^.!]*\?)',
        ]
        
        self.yes_no_indicators = [
            'yes or no', 'y/n', 'yes/no', 'true/false', 'agree/disagree'
        ]
        
        self.multiple_choice_indicators = [
            'choose', 'select', 'pick', 'option', 'either', 'or'
        ]
        
        self.rating_indicators = [
            'scale', 'rate', 'rating', '1 to', '1-', 'out of'
        ]
    
    def extract_questions_from_prompt(self, prompt: str) -> List[ExtractedQuestion]:
        """Extract questions from an LLM prompt"""
        questions = []
        
        # Clean the prompt
        cleaned_prompt = self._clean_prompt(prompt)
        
        # Extract questions using patterns
        for pattern in self.question_patterns:
            matches = re.finditer(pattern, cleaned_prompt, re.IGNORECASE | re.MULTILINE)
            for match in matches:
                question_text = match.group(1).strip()
                if self._is_valid_question(question_text):
                    question = self._analyze_question(question_text, cleaned_prompt)
                    if question not in [q.text for q in questions]:  # Avoid duplicates
                        questions.append(question)
        
        # Extract structured questions (like numbered lists)
        structured_questions = self._extract_structured_questions(cleaned_prompt)
        for q in structured_questions:
            if q.text not in [existing.text for existing in questions]:
                questions.append(q)
        
        return questions
    
    def _clean_prompt(self, prompt: str) -> str:
        """Clean and normalize the prompt text"""
        # Remove excessive whitespace
        cleaned = re.sub(r'\s+', ' ', prompt)
        
        # Remove common prompt artifacts
        cleaned = re.sub(r'###?\s*', '', cleaned)
        cleaned = re.sub(r'---+', '', cleaned)
        cleaned = re.sub(r'\*\*([^*]+)\*\*', r'\1', cleaned)  # Remove bold markdown
        
        return cleaned
    
    def _is_valid_question(self, text: str) -> bool:
        """Check if extracted text is a valid question"""
        if len(text) < 10 or len(text) > 500:
            return False
        
        # Must contain a question mark
        if '?' not in text:
            return False
        
        # Filter out meta-questions about the prompt itself
        meta_indicators = ['prompt', 'instruction', 'guideline', 'example', 'template']
        if any(indicator in text.lower() for indicator in meta_indicators):
            return False
        
        return True
    
    def _analyze_question(self, question_text: str, context: str) -> ExtractedQuestion:
        """Analyze a question to determine its type and options"""
        question_lower = question_text.lower()
        
        # Determine question type
        question_type = "text"
        options = []
        
        # Check for yes/no questions
        if any(indicator in question_lower for indicator in self.yes_no_indicators):
            question_type = "yes_no"
            options = ["Yes", "No"]
        
        # Check for rating questions
        elif any(indicator in question_lower for indicator in self.rating_indicators):
            question_type = "rating"
            # Extract rating scale if possible
            scale_match = re.search(r'(\d+)\s*(?:to|-)?\s*(\d+)', question_text)
            if scale_match:
                start, end = int(scale_match.group(1)), int(scale_match.group(2))
                options = [str(i) for i in range(start, end + 1)]
            else:
                options = ["1", "2", "3", "4", "5"]  # Default 1-5 scale
        
        # Check for multiple choice
        elif any(indicator in question_lower for indicator in self.multiple_choice_indicators):
            question_type = "multiple_choice"
            options = self._extract_options_from_question(question_text)
        
        # Extract options from context if not found in question
        if not options and question_type == "multiple_choice":
            options = self._extract_options_from_context(question_text, context)
        
        # Determine category
        category = self._determine_category(question_text)
        
        return ExtractedQuestion(
            text=question_text,
            question_type=question_type,
            options=options,
            required=True,
            category=category
        )
    
    def _extract_options_from_question(self, question_text: str) -> List[str]:
        """Extract options directly from the question text"""
        options = []
        
        # Look for options separated by "or"
        or_pattern = r'(?:^|[^a-zA-Z])([a-zA-Z][^,?]*?)\s+or\s+([a-zA-Z][^,?]*?)(?:[,?]|$)'
        matches = re.findall(or_pattern, question_text, re.IGNORECASE)
        for match in matches:
            options.extend([opt.strip() for opt in match if opt.strip()])
        
        # Look for parenthetical options
        paren_pattern = r'\(([^)]+)\)'
        paren_matches = re.findall(paren_pattern, question_text)
        for match in paren_matches:
            if ',' in match or '/' in match or 'or' in match.lower():
                opts = re.split(r'[,/]|or', match, flags=re.IGNORECASE)
                options.extend([opt.strip() for opt in opts if opt.strip()])
        
        # Look for dash-separated options
        dash_pattern = r'—([^—]+)—'
        dash_matches = re.findall(dash_pattern, question_text)
        for match in dash_matches:
            if ',' in match:
                opts = match.split(',')
                options.extend([opt.strip() for opt in opts if opt.strip()])
        
        return list(set(options))  # Remove duplicates
    
    def _extract_options_from_context(self, question_text: str, context: str) -> List[str]:
        """Extract options from surrounding context"""
        options = []
        
        # Find the question in context and look for nearby options
        question_pos = context.find(question_text)
        if question_pos != -1:
            # Look in the next 200 characters for options
            context_after = context[question_pos:question_pos + 200]
            
            # Look for bullet points or numbered lists
            bullet_pattern = r'[•\-\*]\s*([^•\-\*\n]+)'
            bullets = re.findall(bullet_pattern, context_after)
            if bullets:
                options.extend([b.strip() for b in bullets if b.strip()])
        
        return options
    
    def _extract_structured_questions(self, prompt: str) -> List[ExtractedQuestion]:
        """Extract questions from structured sections"""
        questions = []
        
        # Look for numbered or lettered sections
        section_pattern = r'(?:^|\n)\s*(?:[A-Z]\.?|[0-9]+\.?)\s*([^:\n]*:?\s*[^?\n]*\?[^?\n]*)'
        matches = re.finditer(section_pattern, prompt, re.MULTILINE)
        
        for match in matches:
            question_text = match.group(1).strip()
            if self._is_valid_question(question_text):
                question = self._analyze_question(question_text, prompt)
                questions.append(question)
        
        return questions
    
    def _determine_category(self, question_text: str) -> str:
        """Determine the category/topic of a question"""
        categories = {
            'demographics': ['age', 'gender', 'location', 'education', 'income', 'employment'],
            'satisfaction': ['satisfied', 'happy', 'pleased', 'rating', 'experience'],
            'preferences': ['prefer', 'favorite', 'like', 'choose', 'better'],
            'behavior': ['do you', 'have you', 'would you', 'behavior', 'habit'],
            'opinion': ['think', 'believe', 'opinion', 'feel', 'view'],
            'economic': ['money', 'cost', 'price', 'investment', 'financial', 'economic', 'inflation'],
            'product': ['product', 'service', 'feature', 'quality', 'performance'],
            'future': ['future', 'plan', 'will', 'expect', 'predict']
        }
        
        question_lower = question_text.lower()
        
        for category, keywords in categories.items():
            if any(keyword in question_lower for keyword in keywords):
                return category
        
        return 'general'
    
    def generate_survey_yaml(
        self, 
        questions: List[ExtractedQuestion], 
        survey_name: str,
        title: str = None,
        description: str = None,
        introduction: str = None,
        conclusion: str = None
    ) -> str:
        """Generate YAML content from extracted questions"""
        
        if not title:
            title = survey_name.replace('_', ' ').title()
        
        if not description:
            description = f"Survey generated from LLM prompt with {len(questions)} questions"
        
        if not introduction:
            introduction = "Thank you for participating in our survey. Your responses are valuable to us."
        
        if not conclusion:
            conclusion = "Thank you for your time and responses!"
        
        # Build survey structure
        survey_data = {
            'title': title,
            'description': description,
            'introduction': introduction,
            'conclusion': conclusion,
            'max_duration_minutes': max(5, len(questions) * 2),  # Estimate 2 minutes per question
            'questions': []
        }
        
        # Add questions
        for i, question in enumerate(questions):
            question_data = {
                'id': f"q{i+1}_{self._generate_question_id(question.text)}",
                'text': question.text,
                'type': question.question_type,
                'required': question.required,
                'order': i + 1
            }
            
            if question.options:
                question_data['options'] = question.options
            
            if question.category:
                question_data['category'] = question.category
            
            survey_data['questions'].append(question_data)
        
        # Convert to YAML
        if YAML_AVAILABLE:
            return yaml.dump(survey_data, default_flow_style=False, sort_keys=False, allow_unicode=True)
        else:
            # Fallback to JSON if YAML not available
            return json.dumps(survey_data, indent=2, ensure_ascii=False)
    
    def _generate_question_id(self, question_text: str) -> str:
        """Generate a short ID from question text"""
        # Take first few words and clean them
        words = re.findall(r'\w+', question_text.lower())[:3]
        return '_'.join(words)
    
    async def generate_from_prompt_file(
        self, 
        prompt_file: Path, 
        output_file: Path = None,
        survey_name: str = None
    ) -> Optional[str]:
        """Generate survey YAML from a prompt file"""
        try:
            # Read prompt file
            if prompt_file.suffix.lower() == '.json':
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    prompt = data.get('llm', {}).get('prompt', '')
                    if not prompt:
                        prompt = str(data)  # Fallback to entire JSON as string
            else:
                with open(prompt_file, 'r', encoding='utf-8') as f:
                    prompt = f.read()
            
            if not prompt:
                logger.error(f"No prompt content found in {prompt_file}")
                return None
            
            # Extract questions
            questions = self.extract_questions_from_prompt(prompt)
            
            if not questions:
                logger.warning(f"No questions extracted from {prompt_file}")
                return None
            
            # Generate survey name if not provided
            if not survey_name:
                survey_name = prompt_file.stem
            
            # Generate YAML
            yaml_content = self.generate_survey_yaml(
                questions=questions,
                survey_name=survey_name,
                title=f"{survey_name.replace('_', ' ').title()} Survey",
                description=f"Survey generated from {prompt_file.name}"
            )
            
            # Save to file if output path provided
            if output_file:
                output_file.parent.mkdir(parents=True, exist_ok=True)
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(yaml_content)
                logger.info(f"Generated survey saved to {output_file}")
            
            return yaml_content
            
        except Exception as e:
            logger.error(f"Error generating survey from {prompt_file}: {e}")
            return None
    
    async def generate_from_text_prompt(
        self,
        prompt_text: str,
        survey_name: str,
        title: str = None,
        description: str = None
    ) -> Optional[str]:
        """Generate survey YAML from text prompt"""
        try:
            # Extract questions
            questions = self.extract_questions_from_prompt(prompt_text)
            
            if not questions:
                logger.warning("No questions extracted from prompt text")
                return None
            
            # Generate YAML
            yaml_content = self.generate_survey_yaml(
                questions=questions,
                survey_name=survey_name,
                title=title,
                description=description
            )
            
            return yaml_content
            
        except Exception as e:
            logger.error(f"Error generating survey from text prompt: {e}")
            return None
