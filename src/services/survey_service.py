import asyncio
import logging
from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

# Try to import yaml, provide fallback if not available
try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    yaml = None

logger = logging.getLogger(__name__)


@dataclass
class SurveyQuestion:
    """Represents a single survey question"""
    id: str
    text: str
    type: str = "text"  # text, multiple_choice, rating, yes_no
    options: List[str] = field(default_factory=list)  # For multiple choice questions
    required: bool = True
    order: int = 0
    
    def __post_init__(self):
        """Validate question data"""
        if self.type == "multiple_choice" and not self.options:
            raise ValueError(f"Multiple choice question '{self.id}' must have options")


@dataclass
class UserAnswer:
    """Represents a user's answer to a question"""
    question_id: str
    answer: str = ""
    answered_at: Optional[datetime] = None
    is_complete: bool = False
    
    def record_answer(self, answer: str):
        """Record an answer for this question"""
        self.answer = answer
        self.answered_at = datetime.now()
        self.is_complete = True


@dataclass
class SurveyDefinition:
    """Represents a complete survey definition"""
    name: str
    title: str
    description: str = ""
    questions: List[SurveyQuestion] = field(default_factory=list)
    introduction: str = ""
    conclusion: str = ""
    max_duration_minutes: int = 10
    
    def get_question_by_id(self, question_id: str) -> Optional[SurveyQuestion]:
        """Get a question by its ID"""
        return next((q for q in self.questions if q.id == question_id), None)
    
    def get_questions_in_order(self) -> List[SurveyQuestion]:
        """Get questions sorted by order"""
        return sorted(self.questions, key=lambda q: q.order)


@dataclass
class UserAnswers:
    """Manages user answers for a survey session"""
    survey_name: str
    participant_id: str
    answers: Dict[str, UserAnswer] = field(default_factory=dict)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    current_question_index: int = 0
    
    def __post_init__(self):
        if self.started_at is None:
            self.started_at = datetime.now()
    
    def get_answer(self, question_id: str) -> Optional[UserAnswer]:
        """Get answer for a specific question"""
        return self.answers.get(question_id)
    
    def record_answer(self, question_id: str, answer: str):
        """Record an answer for a question"""
        if question_id not in self.answers:
            self.answers[question_id] = UserAnswer(question_id=question_id)
        
        self.answers[question_id].record_answer(answer)
        logger.info(f"Recorded answer for question '{question_id}': {answer}")
    
    def get_next_unanswered_question(self, survey: SurveyDefinition) -> Optional[SurveyQuestion]:
        """Get the next unanswered question in order"""
        questions = survey.get_questions_in_order()
        
        for question in questions:
            answer = self.get_answer(question.id)
            if not answer or not answer.is_complete:
                return question
        
        return None
    
    def is_complete(self, survey: SurveyDefinition) -> bool:
        """Check if all required questions have been answered"""
        required_questions = [q for q in survey.questions if q.required]
        
        for question in required_questions:
            answer = self.get_answer(question.id)
            if not answer or not answer.is_complete:
                return False
        
        return True
    
    def mark_completed(self):
        """Mark the survey as completed"""
        self.completed_at = datetime.now()
    
    def get_completion_percentage(self, survey: SurveyDefinition) -> float:
        """Get completion percentage"""
        total_questions = len([q for q in survey.questions if q.required])
        if total_questions == 0:
            return 100.0
        
        answered_questions = len([
            q for q in survey.questions 
            if q.required and self.get_answer(q.id) and self.get_answer(q.id).is_complete
        ])
        
        return (answered_questions / total_questions) * 100.0


class SurveyService:
    """Service for managing survey definitions and user sessions"""
    
    def __init__(self, surveys_directory: Union[str, Path] = None):
        """
        Initialize survey service.
        
        Args:
            surveys_directory: Directory containing YAML survey files
        """
        if surveys_directory is None:
            surveys_directory = Path(__file__).parent.parent / "surveys"
        
        self.surveys_directory = Path(surveys_directory)
        self._surveys_cache: Dict[str, SurveyDefinition] = {}
        self._user_sessions: Dict[str, UserAnswers] = {}
        
        # Ensure surveys directory exists
        self.surveys_directory.mkdir(exist_ok=True)
    
    async def load_survey(self, survey_name: str) -> Optional[SurveyDefinition]:
        """Load a survey definition from YAML file"""
        # Check cache first
        if survey_name in self._surveys_cache:
            return self._surveys_cache[survey_name]

        # Check if YAML is available
        if not YAML_AVAILABLE:
            logger.warning("PyYAML not available, using built-in survey definitions")
            return self._get_builtin_survey(survey_name)

        survey_file = self.surveys_directory / f"{survey_name}.yaml"

        if not survey_file.exists():
            logger.warning(f"Survey file not found: {survey_file}, trying built-in surveys")
            return self._get_builtin_survey(survey_name)

        try:
            with open(survey_file, 'r', encoding='utf-8') as f:
                survey_data = yaml.safe_load(f)

            # Parse survey definition
            survey = self._parse_survey_definition(survey_name, survey_data)

            # Cache the survey
            self._surveys_cache[survey_name] = survey

            logger.info(f"Loaded survey '{survey_name}' with {len(survey.questions)} questions")
            return survey

        except Exception as e:
            logger.error(f"Error loading survey '{survey_name}': {e}")
            logger.info("Falling back to built-in survey definitions")
            return self._get_builtin_survey(survey_name)
    
    def _get_builtin_survey(self, survey_name: str) -> Optional[SurveyDefinition]:
        """Get built-in survey definitions when YAML files are not available"""
        builtin_surveys = {
            "simple_preference": {
                'title': 'Simple Preference Survey',
                'description': 'A quick survey about your preferences',
                'introduction': 'Hi! We have just one quick question for you today.',
                'conclusion': 'Thank you for sharing your preference with us!',
                'max_duration_minutes': 2,
                'questions': [
                    {
                        'id': 'ice_cream_preference',
                        'text': 'Do you prefer chocolate or vanilla ice cream?',
                        'type': 'multiple_choice',
                        'options': ['Chocolate', 'Vanilla', 'Both equally', 'Neither', "I don't eat ice cream"],
                        'required': True,
                        'order': 1
                    }
                ]
            },
            "customer_satisfaction": {
                'title': 'Customer Satisfaction Survey',
                'description': 'A brief survey to understand customer satisfaction with our services',
                'introduction': 'Thank you for taking the time to participate in our customer satisfaction survey.',
                'conclusion': 'Thank you for your time and feedback. Have a great day!',
                'max_duration_minutes': 5,
                'questions': [
                    {
                        'id': 'satisfaction_rating',
                        'text': 'On a scale of 1 to 10, how satisfied are you with our service?',
                        'type': 'rating',
                        'options': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10'],
                        'required': True,
                        'order': 1
                    },
                    {
                        'id': 'recommendation',
                        'text': 'Would you recommend our service to a friend or colleague?',
                        'type': 'yes_no',
                        'options': ['Yes', 'No'],
                        'required': True,
                        'order': 2
                    }
                ]
            }
        }

        if survey_name in builtin_surveys:
            survey_data = builtin_surveys[survey_name]
            survey = self._parse_survey_definition(survey_name, survey_data)
            self._surveys_cache[survey_name] = survey
            logger.info(f"Loaded built-in survey '{survey_name}' with {len(survey.questions)} questions")
            return survey

        logger.error(f"Survey '{survey_name}' not found in built-in surveys")
        return None

    def _parse_survey_definition(self, survey_name: str, survey_data: dict) -> SurveyDefinition:
        """Parse YAML data into SurveyDefinition"""
        questions = []
        
        for i, question_data in enumerate(survey_data.get('questions', [])):
            question = SurveyQuestion(
                id=question_data.get('id', f"q{i+1}"),
                text=question_data['text'],
                type=question_data.get('type', 'text'),
                options=question_data.get('options', []),
                required=question_data.get('required', True),
                order=question_data.get('order', i)
            )
            questions.append(question)
        
        return SurveyDefinition(
            name=survey_name,
            title=survey_data.get('title', survey_name),
            description=survey_data.get('description', ''),
            questions=questions,
            introduction=survey_data.get('introduction', ''),
            conclusion=survey_data.get('conclusion', ''),
            max_duration_minutes=survey_data.get('max_duration_minutes', 10)
        )
    
    async def list_available_surveys(self) -> List[str]:
        """List all available survey names"""
        surveys = set()

        # Add built-in surveys
        surveys.update(["simple_preference", "customer_satisfaction"])

        # Add YAML file surveys if available
        if YAML_AVAILABLE and self.surveys_directory.exists():
            survey_files = list(self.surveys_directory.glob("*.yaml"))
            surveys.update([f.stem for f in survey_files])

        return sorted(list(surveys))
    
    async def create_user_session(self, survey_name: str, participant_id: str) -> Optional[UserAnswers]:
        """Create a new user session for a survey"""
        survey = await self.load_survey(survey_name)
        if not survey:
            return None
        
        session_key = f"{survey_name}:{participant_id}"
        
        # Initialize user answers for all questions
        user_answers = UserAnswers(
            survey_name=survey_name,
            participant_id=participant_id
        )
        
        # Pre-create answer objects for all questions
        for question in survey.questions:
            user_answers.answers[question.id] = UserAnswer(question_id=question.id)
        
        self._user_sessions[session_key] = user_answers
        
        logger.info(f"Created user session for survey '{survey_name}', participant '{participant_id}'")
        return user_answers
    
    async def get_user_session(self, survey_name: str, participant_id: str) -> Optional[UserAnswers]:
        """Get existing user session"""
        session_key = f"{survey_name}:{participant_id}"
        return self._user_sessions.get(session_key)
    
    async def get_or_create_session(self, survey_name: str, participant_id: str) -> Optional[UserAnswers]:
        """Get existing session or create new one"""
        session = await self.get_user_session(survey_name, participant_id)
        if session:
            return session
        
        return await self.create_user_session(survey_name, participant_id)
    
    async def record_answer(self, survey_name: str, participant_id: str, question_id: str, answer: str) -> bool:
        """Record an answer for a user"""
        session = await self.get_user_session(survey_name, participant_id)
        if not session:
            logger.error(f"No session found for survey '{survey_name}', participant '{participant_id}'")
            return False
        
        session.record_answer(question_id, answer)
        return True
    
    async def get_next_question(self, survey_name: str, participant_id: str) -> Optional[SurveyQuestion]:
        """Get the next unanswered question for a user"""
        survey = await self.load_survey(survey_name)
        session = await self.get_user_session(survey_name, participant_id)
        
        if not survey or not session:
            return None
        
        return session.get_next_unanswered_question(survey)
    
    async def is_survey_complete(self, survey_name: str, participant_id: str) -> bool:
        """Check if user has completed the survey"""
        survey = await self.load_survey(survey_name)
        session = await self.get_user_session(survey_name, participant_id)
        
        if not survey or not session:
            return False
        
        return session.is_complete(survey)
    
    async def get_survey_progress(self, survey_name: str, participant_id: str) -> Dict[str, Any]:
        """Get survey progress information"""
        survey = await self.load_survey(survey_name)
        session = await self.get_user_session(survey_name, participant_id)
        
        if not survey or not session:
            return {}
        
        return {
            "survey_name": survey_name,
            "participant_id": participant_id,
            "completion_percentage": session.get_completion_percentage(survey),
            "is_complete": session.is_complete(survey),
            "total_questions": len(survey.questions),
            "answered_questions": len([a for a in session.answers.values() if a.is_complete]),
            "started_at": session.started_at,
            "completed_at": session.completed_at
        }
    
    def clear_cache(self):
        """Clear the surveys cache"""
        self._surveys_cache.clear()
    
    def clear_sessions(self):
        """Clear all user sessions"""
        self._user_sessions.clear()
