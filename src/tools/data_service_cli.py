#!/usr/bin/env python3
"""
Command-line tool for managing call queue data using CallQueueDataService.
Provides easy access to data operations without writing code.
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.call_queue_data_service import CSVCallQueueDataService
from services.call_queue_handler import CallStatus

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


async def list_records(csv_file: Path, status_filter: Optional[str] = None):
    """List all records or filter by status"""
    service = CSVCallQueueDataService(csv_file)
    
    if status_filter:
        if status_filter == "pending":
            records = await service.load_pending_records()
        else:
            all_records = await service.load_all_records()
            records = [r for r in all_records if r.status == status_filter]
        print(f"\n📋 Records with status '{status_filter}':")
    else:
        records = await service.load_all_records()
        print(f"\n📋 All records:")
    
    if not records:
        print("  No records found")
        return
    
    print(f"  Found {len(records)} record(s):")
    print()
    
    for record in records:
        print(f"  Row {record.row_index}: {record.phone_number}")
        print(f"    Question: {record.question}")
        print(f"    Answer: {record.answer or '(none)'}")
        print(f"    Status: {record.status or 'pending'}")
        if record.details:
            print(f"    Details: {record.details}")
        print()


async def add_record(csv_file: Path, phone_number: str, question: str):
    """Add a new record"""
    service = CSVCallQueueDataService(csv_file)
    
    row_index = await service.add_record(phone_number, question)
    print(f"✅ Added new record at row {row_index}")
    print(f"   Phone: {phone_number}")
    print(f"   Question: {question}")


async def update_status(csv_file: Path, row_index: int, status: str, details: str = ""):
    """Update record status"""
    service = CSVCallQueueDataService(csv_file)
    
    # Validate status
    valid_statuses = [s.value for s in CallStatus]
    if status not in valid_statuses:
        print(f"❌ Invalid status '{status}'. Valid options: {', '.join(valid_statuses)}")
        return
    
    success = await service.update_record_status(row_index, status, details)
    
    if success:
        print(f"✅ Updated row {row_index} status to '{status}'")
        if details:
            print(f"   Details: {details}")
    else:
        print(f"❌ Failed to update row {row_index} (row not found)")


async def update_answer(csv_file: Path, row_index: int, answer: str):
    """Update record answer"""
    service = CSVCallQueueDataService(csv_file)
    
    success = await service.update_record_answer(row_index, answer)
    
    if success:
        print(f"✅ Updated row {row_index} answer: {answer}")
    else:
        print(f"❌ Failed to update row {row_index} (row not found)")


async def show_stats(csv_file: Path):
    """Show summary statistics"""
    service = CSVCallQueueDataService(csv_file)
    
    stats = await service.get_summary_stats()
    
    print("\n📊 Summary Statistics:")
    print(f"  Total records: {stats['total']}")
    print(f"  Pending: {stats['pending']}")
    print(f"  Dialing: {stats['dialing']}")
    print(f"  Ringing: {stats['ringing']}")
    print(f"  Active: {stats['active']}")
    print(f"  Completed: {stats['completed']}")
    print(f"  Timeout: {stats['timeout']}")
    print(f"  Error: {stats['error']}")


async def export_pending(csv_file: Path, output_file: Optional[Path] = None):
    """Export pending records to a new CSV file"""
    service = CSVCallQueueDataService(csv_file)
    
    pending_records = await service.load_pending_records()
    
    if not pending_records:
        print("📭 No pending records to export")
        return
    
    if output_file is None:
        output_file = csv_file.parent / f"pending_{csv_file.name}"
    
    # Create new service for output file
    output_service = CSVCallQueueDataService(output_file)
    
    # Add all pending records to output file
    for record in pending_records:
        await output_service.add_record(record.phone_number, record.question)
    
    print(f"✅ Exported {len(pending_records)} pending records to {output_file}")


async def main():
    parser = argparse.ArgumentParser(
        description="Call Queue Data Service CLI Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all records
  python data_service_cli.py survey_data.csv list

  # List only pending records
  python data_service_cli.py survey_data.csv list --status pending

  # Add a new record
  python data_service_cli.py survey_data.csv add "+1234567890" "How satisfied are you?"

  # Update status
  python data_service_cli.py survey_data.csv update-status 1 completed "Call finished"

  # Update answer
  python data_service_cli.py survey_data.csv update-answer 1 "Very satisfied"

  # Show statistics
  python data_service_cli.py survey_data.csv stats

  # Export pending records
  python data_service_cli.py survey_data.csv export-pending --output pending_calls.csv
        """
    )
    
    parser.add_argument("csv_file", type=Path, help="Path to CSV file")
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # List command
    list_parser = subparsers.add_parser("list", help="List records")
    list_parser.add_argument("--status", help="Filter by status (pending, dialing, ringing, active, completed, timeout, error)")
    
    # Add command
    add_parser = subparsers.add_parser("add", help="Add new record")
    add_parser.add_argument("phone_number", help="Phone number")
    add_parser.add_argument("question", help="Question to ask")
    
    # Update status command
    update_status_parser = subparsers.add_parser("update-status", help="Update record status")
    update_status_parser.add_argument("row_index", type=int, help="Row index to update")
    update_status_parser.add_argument("status", help="New status")
    update_status_parser.add_argument("--details", default="", help="Additional details")
    
    # Update answer command
    update_answer_parser = subparsers.add_parser("update-answer", help="Update record answer")
    update_answer_parser.add_argument("row_index", type=int, help="Row index to update")
    update_answer_parser.add_argument("answer", help="Answer text")
    
    # Stats command
    subparsers.add_parser("stats", help="Show summary statistics")
    
    # Export pending command
    export_parser = subparsers.add_parser("export-pending", help="Export pending records")
    export_parser.add_argument("--output", type=Path, help="Output file path")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == "list":
            await list_records(args.csv_file, args.status)
        
        elif args.command == "add":
            await add_record(args.csv_file, args.phone_number, args.question)
        
        elif args.command == "update-status":
            await update_status(args.csv_file, args.row_index, args.status, args.details)
        
        elif args.command == "update-answer":
            await update_answer(args.csv_file, args.row_index, args.answer)
        
        elif args.command == "stats":
            await show_stats(args.csv_file)
        
        elif args.command == "export-pending":
            await export_pending(args.csv_file, args.output)
        
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
