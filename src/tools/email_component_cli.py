#!/usr/bin/env python3
"""
CLI tool for testing and demonstrating the email collection component.
"""

import argparse
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.email_component import <PERSON>ailCollectionComponent, EmailValidator, validate_email, spell_out_email


def test_email_validation():
    """Test email validation with various examples"""
    print("🧪 Email Validation Tests")
    print("=" * 50)
    
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "invalid-email",
        "<EMAIL>",  # Typo
        "test@",
        "@domain.com",
        "user@domain",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        ""
    ]
    
    validator = EmailValidator()
    
    for email in test_emails:
        print(f"\n📧 Testing: '{email}'")
        result = validator.validate_email(email)
        
        print(f"   Valid: {'✅' if result.is_valid else '❌'}")
        print(f"   Formatted: {result.formatted_email}")
        
        if result.issues:
            print(f"   Issues: {', '.join(result.issues)}")
        
        if result.suggestions:
            print(f"   Suggestions: {', '.join(result.suggestions)}")


def test_email_spelling():
    """Test email spelling functionality"""
    print("\n🔤 Email Spelling Tests")
    print("=" * 50)
    
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    validator = EmailValidator()
    
    for email in test_emails:
        print(f"\n📧 Email: {email}")
        spelled = validator.spell_out_email(email)
        readback = validator.format_for_readback(email)
        
        print(f"   Spelled out: {spelled}")
        print(f"   Read-back format: {readback}")


def interactive_email_collection():
    """Interactive email collection simulation"""
    print("\n🎯 Interactive Email Collection Simulation")
    print("=" * 50)
    
    component = EmailCollectionComponent()
    
    print("\n📞 Simulating phone call email collection...")
    print(f"\nAgent: {component.get_initial_request()}")
    
    while True:
        try:
            user_input = input("\nUser: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'done']:
                print("\n👋 Simulation ended.")
                break
            
            # Process the email input
            result = component.process_email_input(user_input)
            
            print(f"\nAgent: {result['response_message']}")
            
            if result['next_action'] == 'readback_confirmation':
                # Wait for confirmation
                confirmation = input("\nUser (yes/no): ").strip().lower()
                
                confirmed = confirmation in ['yes', 'y', 'correct', 'right', 'that\'s right']
                confirmation_result = component.handle_confirmation_response(
                    confirmed, 
                    result['validation'].formatted_email
                )
                
                print(f"\nAgent: {confirmation_result['response_message']}")
                
                if confirmation_result['success']:
                    print(f"\n✅ Email successfully collected: {confirmation_result['final_email']}")
                    break
                elif confirmation_result.get('next_action') != 'request_correction':
                    print("\n❌ Email collection failed after maximum attempts.")
                    break
            
            elif result['next_action'] == 'skip_email':
                print("\n⏭️ Email collection skipped after maximum attempts.")
                break
            
            if result['max_attempts_reached']:
                print("\n⚠️ Maximum attempts reached.")
                break
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break


def show_instructions():
    """Show the extracted email collection instructions"""
    print("\n📋 Email Collection Instructions (from Richard prompt)")
    print("=" * 60)
    
    component = EmailCollectionComponent()
    summary = component.get_collection_summary()
    
    print("\n🎯 Initial Request:")
    print(f"   {summary['instructions']['initial_request']}")
    
    print("\n❓ Clarification Options:")
    for i, clarification in enumerate(summary['instructions']['clarification_options'], 1):
        print(f"   {i}. {clarification}")
    
    print("\n🔄 Read-back Template:")
    print(f"   {summary['instructions']['readback_template']}")
    
    print("\n✅ Success Message:")
    print(f"   {summary['instructions']['success_message']}")
    
    print("\n🔧 Correction Request:")
    print(f"   {summary['instructions']['correction_request']}")
    
    print("\n🛠️ Validation Features:")
    for feature, enabled in summary['validation_features'].items():
        if isinstance(enabled, bool):
            print(f"   {feature}: {'✅' if enabled else '❌'}")
        elif isinstance(enabled, list):
            print(f"   {feature}: {len(enabled)} supported")
    
    print("\n📊 Process Flow:")
    for step in summary['process_flow']:
        print(f"   {step}")


def validate_single_email(email: str):
    """Validate a single email address"""
    print(f"\n📧 Validating: {email}")
    print("=" * 50)
    
    result = validate_email(email)
    
    print(f"Valid: {'✅' if result.is_valid else '❌'}")
    print(f"Original: {result.email}")
    print(f"Formatted: {result.formatted_email}")
    print(f"Spelled out: {spell_out_email(result.formatted_email)}")
    
    if result.issues:
        print(f"Issues: {', '.join(result.issues)}")
    
    if result.suggestions:
        print(f"Suggestions: {', '.join(result.suggestions)}")


def main():
    parser = argparse.ArgumentParser(
        description="Email Collection Component CLI Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Show extracted instructions from Richard prompt
  python email_component_cli.py instructions

  # Test email validation
  python email_component_cli.py test-validation

  # Test email spelling
  python email_component_cli.py test-spelling

  # Interactive email collection simulation
  python email_component_cli.py interactive

  # Validate specific email
  python email_component_cli.py validate "<EMAIL>"
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # Instructions command
    subparsers.add_parser("instructions", help="Show email collection instructions")
    
    # Test commands
    subparsers.add_parser("test-validation", help="Test email validation")
    subparsers.add_parser("test-spelling", help="Test email spelling")
    
    # Interactive command
    subparsers.add_parser("interactive", help="Interactive email collection simulation")
    
    # Validate command
    validate_parser = subparsers.add_parser("validate", help="Validate specific email")
    validate_parser.add_argument("email", help="Email address to validate")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == "instructions":
            show_instructions()
        
        elif args.command == "test-validation":
            test_email_validation()
        
        elif args.command == "test-spelling":
            test_email_spelling()
        
        elif args.command == "interactive":
            interactive_email_collection()
        
        elif args.command == "validate":
            validate_single_email(args.email)
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
