#!/usr/bin/env python3
"""
Email function tool that can be integrated into survey agents.
Provides structured email collection with validation and formatting.
"""

import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.email_component import EmailCollectionComponent, EmailValidator


class EmailFunctionTool:
    """Email collection function tool for survey agents"""
    
    def __init__(self):
        self.email_component = EmailCollectionComponent()
        self.current_email = None
        self.collection_state = "initial"  # initial, collecting, confirming, completed
    
    def get_email_request_message(self) -> str:
        """Get the initial email request message"""
        return self.email_component.get_initial_request()
    
    def process_email_response(self, user_input: str) -> dict:
        """
        Process user's email response and return next action.
        
        Args:
            user_input: User's spoken/typed response
            
        Returns:
            dict with keys:
            - action: 'request_clarification', 'confirm_email', 'complete', 'skip'
            - message: Response message to user
            - email: Validated email (if successful)
            - spelled_email: Spelled out email for confirmation
        """
        if self.collection_state == "initial" or self.collection_state == "collecting":
            # Process email input
            result = self.email_component.process_email_input(user_input)
            
            if result['next_action'] == 'readback_confirmation':
                self.current_email = result['validation'].formatted_email
                self.collection_state = "confirming"
                
                return {
                    'action': 'confirm_email',
                    'message': result['response_message'],
                    'email': self.current_email,
                    'spelled_email': self.email_component.validator.format_for_readback(self.current_email)
                }
            
            elif result['next_action'] == 'request_clarification':
                self.collection_state = "collecting"
                
                return {
                    'action': 'request_clarification',
                    'message': result['response_message'],
                    'email': None,
                    'spelled_email': None
                }
            
            elif result['next_action'] == 'skip_email':
                self.collection_state = "completed"
                
                return {
                    'action': 'skip',
                    'message': result['response_message'],
                    'email': None,
                    'spelled_email': None
                }
        
        return {
            'action': 'error',
            'message': "Unexpected state in email collection",
            'email': None,
            'spelled_email': None
        }
    
    def process_confirmation_response(self, user_confirmed: bool) -> dict:
        """
        Process user's confirmation of the spelled-out email.
        
        Args:
            user_confirmed: True if user confirmed email is correct
            
        Returns:
            dict with action and message
        """
        if self.collection_state != "confirming":
            return {
                'action': 'error',
                'message': "Not in confirmation state",
                'email': None
            }
        
        result = self.email_component.handle_confirmation_response(user_confirmed, self.current_email)
        
        if result['success']:
            self.collection_state = "completed"
            return {
                'action': 'complete',
                'message': result['response_message'],
                'email': result['final_email']
            }
        else:
            if result.get('next_action') == 'request_correction':
                self.collection_state = "collecting"
                return {
                    'action': 'request_clarification',
                    'message': result['response_message'],
                    'email': None
                }
            else:
                self.collection_state = "completed"
                return {
                    'action': 'skip',
                    'message': result['response_message'],
                    'email': None
                }
    
    def reset(self):
        """Reset the tool for a new email collection session"""
        self.email_component.reset()
        self.current_email = None
        self.collection_state = "initial"
    
    def get_state(self) -> dict:
        """Get current state of email collection"""
        return {
            'state': self.collection_state,
            'current_email': self.current_email,
            'attempts': self.email_component.collection_attempts,
            'max_attempts': self.email_component.max_attempts
        }


# Example usage functions that can be used as function tools in agents

def collect_email_address(user_input: str, context: dict = None) -> tuple[str, str]:
    """
    Function tool for collecting email addresses in survey agents.
    
    Args:
        user_input: User's response containing email
        context: Optional context dict to maintain state
        
    Returns:
        tuple: (response_message, action_type)
    """
    if context is None:
        context = {}
    
    # Initialize email tool if not exists
    if 'email_tool' not in context:
        context['email_tool'] = EmailFunctionTool()
    
    email_tool = context['email_tool']
    
    # Process the input
    result = email_tool.process_email_response(user_input)
    
    if result['action'] == 'confirm_email':
        # Store email for confirmation
        context['pending_email'] = result['email']
        return result['message'], "confirm_email"
    
    elif result['action'] == 'request_clarification':
        return result['message'], "request_clarification"
    
    elif result['action'] == 'complete':
        # Email successfully collected
        context['collected_email'] = result['email']
        return result['message'], "email_collected"
    
    elif result['action'] == 'skip':
        return result['message'], "email_skipped"
    
    else:
        return "I'm having trouble with the email collection. Let's continue.", "error"


def confirm_email_address(user_confirmed: bool, context: dict = None) -> tuple[str, str]:
    """
    Function tool for confirming email addresses.
    
    Args:
        user_confirmed: True if user confirmed the email is correct
        context: Context dict containing email tool state
        
    Returns:
        tuple: (response_message, action_type)
    """
    if context is None or 'email_tool' not in context:
        return "Email confirmation error - no active collection.", "error"
    
    email_tool = context['email_tool']
    result = email_tool.process_confirmation_response(user_confirmed)
    
    if result['action'] == 'complete':
        context['collected_email'] = result['email']
        return result['message'], "email_collected"
    
    elif result['action'] == 'request_clarification':
        return result['message'], "request_clarification"
    
    elif result['action'] == 'skip':
        return result['message'], "email_skipped"
    
    else:
        return "Email confirmation error.", "error"


def validate_and_format_email(email: str) -> dict:
    """
    Simple function to validate and format an email address.
    
    Args:
        email: Email address to validate
        
    Returns:
        dict with validation results
    """
    validator = EmailValidator()
    result = validator.validate_email(email)
    
    return {
        'is_valid': result.is_valid,
        'original': result.email,
        'formatted': result.formatted_email,
        'spelled_out': validator.spell_out_email(result.formatted_email),
        'readback_format': validator.format_for_readback(result.formatted_email),
        'issues': result.issues,
        'suggestions': result.suggestions
    }


# Example integration with survey agent
def create_email_collection_instructions() -> dict:
    """
    Create email collection instructions for survey agents.
    
    Returns:
        dict with all necessary instructions and templates
    """
    component = EmailCollectionComponent()
    
    return {
        'initial_request': component.instructions.transition_request,
        'clarification_requests': component.instructions.clarification_requests,
        'readback_template': component.instructions.readback_intro,
        'success_message': component.instructions.confirmation_success,
        'correction_request': component.instructions.correction_request,
        'max_attempts': component.max_attempts,
        'example_usage': {
            'step_1': "Use initial_request to ask for email",
            'step_2': "Process response with collect_email_address()",
            'step_3': "If action is 'confirm_email', use readback_template",
            'step_4': "Process confirmation with confirm_email_address()",
            'step_5': "Handle final result based on action type"
        }
    }


if __name__ == "__main__":
    # Demo the email function tool
    print("📧 Email Function Tool Demo")
    print("=" * 40)
    
    # Create instructions
    instructions = create_email_collection_instructions()
    print(f"Initial request: {instructions['initial_request']}")
    
    # Test email validation
    test_email = "<EMAIL>"
    validation = validate_and_format_email(test_email)
    print(f"\nValidation of '{test_email}':")
    print(f"Valid: {validation['is_valid']}")
    print(f"Formatted: {validation['formatted']}")
    print(f"Spelled out: {validation['spelled_out']}")
    print(f"Suggestions: {validation['suggestions']}")
