#!/usr/bin/env python3
"""
Command-line tool for managing surveys and testing survey functionality.
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.survey_service import SurveyService

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


async def list_surveys():
    """List all available surveys"""
    service = SurveyService()
    
    surveys = await service.list_available_surveys()
    
    print("\n📋 Available Surveys:")
    if not surveys:
        print("  No surveys found")
        return
    
    for survey_name in surveys:
        survey = await service.load_survey(survey_name)
        if survey:
            print(f"\n  📝 {survey_name}")
            print(f"     Title: {survey.title}")
            print(f"     Description: {survey.description}")
            print(f"     Questions: {len(survey.questions)}")
            print(f"     Duration: {survey.max_duration_minutes} minutes")


async def show_survey_details(survey_name: str):
    """Show detailed information about a specific survey"""
    service = SurveyService()
    
    survey = await service.load_survey(survey_name)
    if not survey:
        print(f"❌ Survey '{survey_name}' not found")
        return
    
    print(f"\n📝 Survey: {survey.title}")
    print(f"Description: {survey.description}")
    print(f"Introduction: {survey.introduction}")
    print(f"Conclusion: {survey.conclusion}")
    print(f"Max Duration: {survey.max_duration_minutes} minutes")
    print(f"Total Questions: {len(survey.questions)}")
    
    print("\n📋 Questions:")
    for i, question in enumerate(survey.get_questions_in_order(), 1):
        print(f"\n  Q{i}: {question.text}")
        print(f"      ID: {question.id}")
        print(f"      Type: {question.type}")
        print(f"      Required: {'Yes' if question.required else 'No'}")
        
        if question.options:
            print(f"      Options: {', '.join(question.options)}")


async def test_survey_flow(survey_name: str, participant_id: str = "test_user"):
    """Test a complete survey flow interactively"""
    service = SurveyService()
    
    # Load survey
    survey = await service.load_survey(survey_name)
    if not survey:
        print(f"❌ Survey '{survey_name}' not found")
        return
    
    print(f"\n🚀 Testing Survey: {survey.title}")
    print(f"📝 {survey.description}")
    
    if survey.introduction:
        print(f"\n💬 Introduction: {survey.introduction}")
    
    # Create user session
    session = await service.create_user_session(survey_name, participant_id)
    if not session:
        print("❌ Failed to create user session")
        return
    
    print(f"\n✅ Created session for participant: {participant_id}")
    
    # Process questions interactively
    question_count = 0
    while True:
        # Get next question
        current_question = await service.get_next_question(survey_name, participant_id)
        
        if not current_question:
            break
        
        question_count += 1
        print(f"\n❓ Question {question_count}: {current_question.text}")
        
        # Show options if available
        if current_question.options:
            print("   Options:")
            for i, option in enumerate(current_question.options, 1):
                print(f"     {i}. {option}")
        
        # Get user input
        if current_question.type == "multiple_choice" and current_question.options:
            while True:
                try:
                    choice = input(f"\n   Enter choice (1-{len(current_question.options)}): ").strip()
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(current_question.options):
                        answer = current_question.options[choice_idx]
                        break
                    else:
                        print(f"   Please enter a number between 1 and {len(current_question.options)}")
                except ValueError:
                    print("   Please enter a valid number")
        else:
            answer = input("   Your answer: ").strip()
        
        # Record answer
        await service.record_answer(survey_name, participant_id, current_question.id, answer)
        print(f"   ✅ Recorded: {answer}")
        
        # Show progress
        progress = await service.get_survey_progress(survey_name, participant_id)
        print(f"   📊 Progress: {progress['completion_percentage']:.1f}%")
        
        # Check if complete
        if await service.is_survey_complete(survey_name, participant_id):
            break
    
    # Survey completed
    print(f"\n🎉 Survey Completed!")
    if survey.conclusion:
        print(f"💬 {survey.conclusion}")
    
    # Show final results
    final_session = await service.get_user_session(survey_name, participant_id)
    print(f"\n📊 Final Results:")
    print(f"   Started: {final_session.started_at}")
    print(f"   Questions Answered: {len([a for a in final_session.answers.values() if a.is_complete])}")
    
    print(f"\n📝 Answers:")
    for question in survey.get_questions_in_order():
        answer = final_session.get_answer(question.id)
        if answer and answer.is_complete:
            print(f"   {question.text}")
            print(f"   → {answer.answer}")


async def simulate_survey_responses(survey_name: str, num_participants: int = 3):
    """Simulate multiple participants taking a survey"""
    service = SurveyService()
    
    survey = await service.load_survey(survey_name)
    if not survey:
        print(f"❌ Survey '{survey_name}' not found")
        return
    
    print(f"\n🤖 Simulating {num_participants} participants for: {survey.title}")
    
    # Predefined responses for simulation
    sample_responses = {
        "ice_cream_preference": ["Chocolate", "Vanilla", "Both equally"],
        "satisfaction_rating": ["8", "9", "7", "10", "6"],
        "recommendation": ["Yes", "Yes", "No", "Yes"],
        "usage_frequency": ["Daily", "Weekly", "Monthly"],
        "ease_of_use": ["4 - Easy", "5 - Very easy", "3 - Neutral"]
    }
    
    questions = survey.get_questions_in_order()
    
    for i in range(num_participants):
        participant_id = f"sim_user_{i+1}"
        print(f"\n👤 Participant {i+1}: {participant_id}")
        
        # Create session
        await service.create_user_session(survey_name, participant_id)
        
        # Answer questions
        for question in questions:
            # Get sample answer
            if question.id in sample_responses:
                answers = sample_responses[question.id]
                answer = answers[i % len(answers)]
            elif question.options:
                answer = question.options[i % len(question.options)]
            else:
                answer = f"Sample answer {i+1}"
            
            await service.record_answer(survey_name, participant_id, question.id, answer)
            print(f"   {question.text} → {answer}")
        
        # Show completion
        progress = await service.get_survey_progress(survey_name, participant_id)
        print(f"   ✅ Completed: {progress['completion_percentage']:.1f}%")
    
    print(f"\n📊 Simulation completed for {num_participants} participants")


async def main():
    parser = argparse.ArgumentParser(
        description="Survey Management CLI Tool",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all available surveys
  python survey_cli.py list

  # Show details of a specific survey
  python survey_cli.py details simple_preference

  # Test a survey interactively
  python survey_cli.py test customer_satisfaction

  # Simulate multiple participants
  python survey_cli.py simulate simple_preference --participants 5
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # List command
    subparsers.add_parser("list", help="List all available surveys")
    
    # Details command
    details_parser = subparsers.add_parser("details", help="Show survey details")
    details_parser.add_argument("survey_name", help="Name of the survey")
    
    # Test command
    test_parser = subparsers.add_parser("test", help="Test survey flow interactively")
    test_parser.add_argument("survey_name", help="Name of the survey")
    test_parser.add_argument("--participant", default="test_user", help="Participant ID")
    
    # Simulate command
    sim_parser = subparsers.add_parser("simulate", help="Simulate multiple participants")
    sim_parser.add_argument("survey_name", help="Name of the survey")
    sim_parser.add_argument("--participants", type=int, default=3, help="Number of participants")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == "list":
            await list_surveys()
        
        elif args.command == "details":
            await show_survey_details(args.survey_name)
        
        elif args.command == "test":
            await test_survey_flow(args.survey_name, args.participant)
        
        elif args.command == "simulate":
            await simulate_survey_responses(args.survey_name, args.participants)
        
    except KeyboardInterrupt:
        print("\n\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
