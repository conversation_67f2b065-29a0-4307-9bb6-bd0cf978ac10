#!/usr/bin/env python3
"""
Command-line tool for generating survey YAML files from LLM prompts.
"""

import argparse
import asyncio
import logging
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from services.survey_generator import SurveyGenerator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)


async def generate_from_file(
    input_file: Path, 
    output_file: Path = None, 
    survey_name: str = None,
    preview_only: bool = False
):
    """Generate survey from a prompt file"""
    generator = SurveyGenerator()
    
    if not input_file.exists():
        print(f"❌ Input file not found: {input_file}")
        return
    
    print(f"📄 Processing prompt file: {input_file}")
    
    # Generate survey
    yaml_content = await generator.generate_from_prompt_file(
        prompt_file=input_file,
        output_file=output_file if not preview_only else None,
        survey_name=survey_name
    )
    
    if not yaml_content:
        print("❌ Failed to generate survey from prompt")
        return
    
    # Extract questions for preview
    questions = generator.extract_questions_from_prompt(
        open(input_file, 'r', encoding='utf-8').read() if input_file.suffix != '.json'
        else str(__import__('json').load(open(input_file, 'r')))
    )
    
    print(f"\n✅ Successfully extracted {len(questions)} questions:")
    for i, question in enumerate(questions, 1):
        print(f"\n  Q{i}: {question.text}")
        print(f"      Type: {question.question_type}")
        if question.options:
            print(f"      Options: {', '.join(question.options)}")
        if question.category:
            print(f"      Category: {question.category}")
    
    if preview_only:
        print(f"\n📋 Generated YAML Preview:")
        print("=" * 50)
        print(yaml_content)
        print("=" * 50)
    else:
        if output_file:
            print(f"\n💾 Survey saved to: {output_file}")
        else:
            print(f"\n📋 Generated YAML:")
            print(yaml_content)


async def generate_from_text(
    prompt_text: str,
    survey_name: str,
    output_file: Path = None,
    title: str = None,
    description: str = None,
    preview_only: bool = False
):
    """Generate survey from text prompt"""
    generator = SurveyGenerator()
    
    print(f"📝 Processing text prompt for survey: {survey_name}")
    
    # Generate survey
    yaml_content = await generator.generate_from_text_prompt(
        prompt_text=prompt_text,
        survey_name=survey_name,
        title=title,
        description=description
    )
    
    if not yaml_content:
        print("❌ Failed to generate survey from prompt text")
        return
    
    # Extract questions for preview
    questions = generator.extract_questions_from_prompt(prompt_text)
    
    print(f"\n✅ Successfully extracted {len(questions)} questions:")
    for i, question in enumerate(questions, 1):
        print(f"\n  Q{i}: {question.text}")
        print(f"      Type: {question.question_type}")
        if question.options:
            print(f"      Options: {', '.join(question.options)}")
        if question.category:
            print(f"      Category: {question.category}")
    
    if not preview_only and output_file:
        output_file.parent.mkdir(parents=True, exist_ok=True)
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(yaml_content)
        print(f"\n💾 Survey saved to: {output_file}")
    
    if preview_only or not output_file:
        print(f"\n📋 Generated YAML:")
        print("=" * 50)
        print(yaml_content)
        print("=" * 50)


async def analyze_prompt(input_file: Path):
    """Analyze a prompt file and show what questions would be extracted"""
    generator = SurveyGenerator()
    
    if not input_file.exists():
        print(f"❌ Input file not found: {input_file}")
        return
    
    print(f"🔍 Analyzing prompt file: {input_file}")
    
    try:
        # Read prompt content
        if input_file.suffix.lower() == '.json':
            import json
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                prompt = data.get('llm', {}).get('prompt', '')
                if not prompt:
                    prompt = str(data)
        else:
            with open(input_file, 'r', encoding='utf-8') as f:
                prompt = f.read()
        
        print(f"📄 Prompt length: {len(prompt)} characters")
        
        # Extract questions
        questions = generator.extract_questions_from_prompt(prompt)
        
        print(f"\n📊 Analysis Results:")
        print(f"   Total questions found: {len(questions)}")
        
        # Group by type
        type_counts = {}
        category_counts = {}
        
        for question in questions:
            type_counts[question.question_type] = type_counts.get(question.question_type, 0) + 1
            if question.category:
                category_counts[question.category] = category_counts.get(question.category, 0) + 1
        
        print(f"\n📈 Question Types:")
        for qtype, count in type_counts.items():
            print(f"   {qtype}: {count}")
        
        if category_counts:
            print(f"\n🏷️  Categories:")
            for category, count in category_counts.items():
                print(f"   {category}: {count}")
        
        print(f"\n❓ Extracted Questions:")
        for i, question in enumerate(questions, 1):
            print(f"\n  Q{i}: {question.text}")
            print(f"      Type: {question.question_type}")
            if question.options:
                print(f"      Options: {', '.join(question.options[:3])}{'...' if len(question.options) > 3 else ''}")
            if question.category:
                print(f"      Category: {question.category}")
        
    except Exception as e:
        print(f"❌ Error analyzing prompt: {e}")


async def interactive_mode():
    """Interactive mode for creating surveys"""
    print("🎯 Interactive Survey Generator")
    print("Enter your survey description or questions (type 'done' when finished):")
    
    lines = []
    while True:
        try:
            line = input("> ")
            if line.lower().strip() == 'done':
                break
            lines.append(line)
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            return
    
    if not lines:
        print("❌ No input provided")
        return
    
    prompt_text = '\n'.join(lines)
    
    # Get survey details
    survey_name = input("\nSurvey name (e.g., customer_feedback): ").strip()
    if not survey_name:
        survey_name = "interactive_survey"
    
    title = input("Survey title (optional): ").strip()
    description = input("Survey description (optional): ").strip()
    
    save_file = input("Save to file? (y/N): ").strip().lower()
    output_file = None
    
    if save_file == 'y':
        filename = input(f"Filename (default: {survey_name}.yaml): ").strip()
        if not filename:
            filename = f"{survey_name}.yaml"
        output_file = Path("src/surveys") / filename
    
    # Generate survey
    await generate_from_text(
        prompt_text=prompt_text,
        survey_name=survey_name,
        output_file=output_file,
        title=title if title else None,
        description=description if description else None,
        preview_only=output_file is None
    )


async def main():
    parser = argparse.ArgumentParser(
        description="Generate survey YAML files from LLM prompts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate from JSON prompt file
  python survey_generator_cli.py from-file amber.json --output economic_survey.yaml

  # Preview without saving
  python survey_generator_cli.py from-file amber.json --preview

  # Generate from text prompt
  python survey_generator_cli.py from-text "What is your favorite color? Do you prefer cats or dogs?" --name preferences

  # Analyze a prompt file
  python survey_generator_cli.py analyze amber.json

  # Interactive mode
  python survey_generator_cli.py interactive
        """
    )
    
    subparsers = parser.add_subparsers(dest="command", help="Available commands")
    
    # From file command
    file_parser = subparsers.add_parser("from-file", help="Generate survey from prompt file")
    file_parser.add_argument("input_file", type=Path, help="Input prompt file (JSON or text)")
    file_parser.add_argument("--output", type=Path, help="Output YAML file")
    file_parser.add_argument("--name", help="Survey name (default: input filename)")
    file_parser.add_argument("--preview", action="store_true", help="Preview only, don't save")
    
    # From text command
    text_parser = subparsers.add_parser("from-text", help="Generate survey from text prompt")
    text_parser.add_argument("prompt", help="Prompt text containing questions")
    text_parser.add_argument("--name", required=True, help="Survey name")
    text_parser.add_argument("--output", type=Path, help="Output YAML file")
    text_parser.add_argument("--title", help="Survey title")
    text_parser.add_argument("--description", help="Survey description")
    text_parser.add_argument("--preview", action="store_true", help="Preview only, don't save")
    
    # Analyze command
    analyze_parser = subparsers.add_parser("analyze", help="Analyze prompt file for questions")
    analyze_parser.add_argument("input_file", type=Path, help="Input prompt file to analyze")
    
    # Interactive command
    subparsers.add_parser("interactive", help="Interactive survey creation mode")
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        if args.command == "from-file":
            await generate_from_file(
                input_file=args.input_file,
                output_file=args.output,
                survey_name=args.name,
                preview_only=args.preview
            )
        
        elif args.command == "from-text":
            await generate_from_text(
                prompt_text=args.prompt,
                survey_name=args.name,
                output_file=args.output,
                title=args.title,
                description=args.description,
                preview_only=args.preview
            )
        
        elif args.command == "analyze":
            await analyze_prompt(args.input_file)
        
        elif args.command == "interactive":
            await interactive_mode()
        
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        logger.error(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
