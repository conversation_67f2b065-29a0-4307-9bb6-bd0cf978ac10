import pytest
import asyncio
import tempfile
from pathlib import Path
from unittest.mock import patch, mock_open
from services.call_queue_data_service import (
    CallRecord, CallQueueDataService, CSVCallQueueDataService
)
from services.call_queue_handler import CallItem, CallStatus


@pytest.fixture
def temp_csv_file():
    """Create a temporary CSV file for testing"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        # Write test data
        f.write("phone_number,question,answer,status,details\n")
        f.write("+1234567890,Test question 1,,pending,\n")
        f.write("+1234567891,Test question 2,Test answer,completed,Call finished\n")
        f.write("+1234567892,Test question 3,,,\n")
        temp_path = Path(f.name)
    
    yield temp_path
    
    # Cleanup
    if temp_path.exists():
        temp_path.unlink()


@pytest.fixture
def empty_csv_file():
    """Create an empty temporary CSV file"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        temp_path = Path(f.name)
    
    # Delete the file so it doesn't exist
    temp_path.unlink()
    
    yield temp_path
    
    # Cleanup
    if temp_path.exists():
        temp_path.unlink()


class TestCallRecord:
    """Test CallRecord functionality"""
    
    def test_call_record_creation(self):
        """Test CallRecord creation and default values"""
        record = CallRecord(
            row_index=1,
            phone_number="+1234567890",
            question="Test question"
        )
        
        assert record.row_index == 1
        assert record.phone_number == "+1234567890"
        assert record.question == "Test question"
        assert record.answer == ""
        assert record.status == ""
        assert record.details == ""
    
    def test_to_call_item(self):
        """Test conversion to CallItem"""
        record = CallRecord(
            row_index=5,
            phone_number="+1234567890",
            question="How are you?",
            answer="Good",
            status="completed",
            details="Call finished"
        )
        
        call_item = record.to_call_item()
        
        assert call_item.phone_number == "+1234567890"
        assert call_item.call_id == "5"
        assert call_item.metadata["question"] == "How are you?"
        assert call_item.metadata["row_index"] == 5
        assert call_item.metadata["answer"] == "Good"
        assert call_item.status == CallStatus.COMPLETED
        assert call_item.details == "Call finished"
    
    def test_to_call_item_pending_status(self):
        """Test conversion to CallItem with pending status"""
        record = CallRecord(
            row_index=1,
            phone_number="+1234567890",
            question="Test question"
        )
        
        call_item = record.to_call_item()
        assert call_item.status == CallStatus.PENDING
    
    def test_from_call_item(self):
        """Test creation from CallItem"""
        call_item = CallItem(
            phone_number="+1234567890",
            metadata={
                "question": "Test question",
                "row_index": 3,
                "answer": "Test answer"
            },
            call_id="3",
            status=CallStatus.ACTIVE,
            details="Call in progress"
        )
        
        record = CallRecord.from_call_item(call_item)
        
        assert record.row_index == 3
        assert record.phone_number == "+1234567890"
        assert record.question == "Test question"
        assert record.answer == "Test answer"
        assert record.status == "active"
        assert record.details == "Call in progress"


class TestCSVCallQueueDataService:
    """Test CSV-based data service"""
    
    @pytest.mark.asyncio
    async def test_load_all_records(self, temp_csv_file):
        """Test loading all records from CSV"""
        service = CSVCallQueueDataService(temp_csv_file)
        records = await service.load_all_records()
        
        assert len(records) == 3
        
        # Check first record
        assert records[0].row_index == 1
        assert records[0].phone_number == "+1234567890"
        assert records[0].question == "Test question 1"
        assert records[0].status == "pending"
        
        # Check second record
        assert records[1].row_index == 2
        assert records[1].phone_number == "+1234567891"
        assert records[1].answer == "Test answer"
        assert records[1].status == "completed"
    
    @pytest.mark.asyncio
    async def test_load_pending_records(self, temp_csv_file):
        """Test loading only pending records"""
        service = CSVCallQueueDataService(temp_csv_file)
        pending_records = await service.load_pending_records()
        
        # Should only return records without answers and status
        assert len(pending_records) == 1
        assert pending_records[0].row_index == 3
        assert pending_records[0].phone_number == "+1234567892"
    
    @pytest.mark.asyncio
    async def test_update_record_status(self, temp_csv_file):
        """Test updating record status"""
        service = CSVCallQueueDataService(temp_csv_file)
        
        # Update status
        result = await service.update_record_status(1, "dialing", "Call started")
        assert result is True
        
        # Verify update
        records = await service.load_all_records()
        updated_record = next(r for r in records if r.row_index == 1)
        assert updated_record.status == "dialing"
        assert updated_record.details == "Call started"
    
    @pytest.mark.asyncio
    async def test_update_record_answer(self, temp_csv_file):
        """Test updating record answer"""
        service = CSVCallQueueDataService(temp_csv_file)
        
        # Update answer
        result = await service.update_record_answer(3, "My answer")
        assert result is True
        
        # Verify update
        records = await service.load_all_records()
        updated_record = next(r for r in records if r.row_index == 3)
        assert updated_record.answer == "My answer"
        assert updated_record.status == "completed"
    
    @pytest.mark.asyncio
    async def test_get_record_by_index(self, temp_csv_file):
        """Test getting specific record by index"""
        service = CSVCallQueueDataService(temp_csv_file)
        
        # Get existing record
        record = await service.get_record_by_index(2)
        assert record is not None
        assert record.phone_number == "+1234567891"
        
        # Get non-existing record
        record = await service.get_record_by_index(999)
        assert record is None
    
    @pytest.mark.asyncio
    async def test_add_record(self, empty_csv_file):
        """Test adding new records"""
        service = CSVCallQueueDataService(empty_csv_file)
        
        # Add first record
        row_index = await service.add_record("+1111111111", "Question 1")
        assert row_index == 1
        
        # Add second record
        row_index = await service.add_record("+2222222222", "Question 2")
        assert row_index == 2
        
        # Verify records were added
        records = await service.load_all_records()
        assert len(records) == 2
        assert records[0].phone_number == "+1111111111"
        assert records[1].phone_number == "+2222222222"
    
    @pytest.mark.asyncio
    async def test_load_pending_call_items(self, temp_csv_file):
        """Test loading pending records as CallItem objects"""
        service = CSVCallQueueDataService(temp_csv_file)
        call_items = await service.load_pending_call_items()
        
        assert len(call_items) == 1
        call_item = call_items[0]
        assert isinstance(call_item, CallItem)
        assert call_item.phone_number == "+1234567892"
        assert call_item.metadata["question"] == "Test question 3"
        assert call_item.status == CallStatus.PENDING
    
    @pytest.mark.asyncio
    async def test_update_call_item_status(self, temp_csv_file):
        """Test updating status using CallItem"""
        service = CSVCallQueueDataService(temp_csv_file)
        
        # Create a CallItem
        call_item = CallItem(
            phone_number="+1234567890",
            metadata={"row_index": 1, "question": "Test"},
            call_id="1"
        )
        
        # Update status
        result = await service.update_call_item_status(call_item, CallStatus.ACTIVE, "Call active")
        assert result is True
        
        # Verify update
        record = await service.get_record_by_index(1)
        assert record.status == "active"
        assert record.details == "Call active"
    
    @pytest.mark.asyncio
    async def test_get_summary_stats(self, temp_csv_file):
        """Test getting summary statistics"""
        service = CSVCallQueueDataService(temp_csv_file)
        stats = await service.get_summary_stats()
        
        assert stats["total"] == 3
        assert stats["pending"] == 2  # Records 1 and 3 (no answer and empty/pending status)
        assert stats["completed"] == 1  # Record 2
    
    @pytest.mark.asyncio
    async def test_ensure_csv_structure_creates_file(self, empty_csv_file):
        """Test that CSV structure is created if file doesn't exist"""
        service = CSVCallQueueDataService(empty_csv_file)
        
        # File shouldn't exist initially
        assert not empty_csv_file.exists()
        
        # This should create the file
        await service._ensure_csv_structure()
        
        # File should now exist with headers
        assert empty_csv_file.exists()
        
        # Verify headers
        with open(empty_csv_file, 'r') as f:
            first_line = f.readline().strip()
            assert first_line == "phone_number,question,answer,status,details"
    
    @pytest.mark.asyncio
    async def test_concurrent_access(self, temp_csv_file):
        """Test concurrent access to CSV file"""
        service = CSVCallQueueDataService(temp_csv_file)
        
        # Simulate concurrent updates
        async def update_status(row_index, status):
            await service.update_record_status(row_index, status, f"Update {row_index}")
        
        # Run concurrent updates
        tasks = [
            update_status(1, "dialing"),
            update_status(2, "ringing"),
            update_status(3, "active")
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All updates should succeed
        assert all(result is True for result in results)
        
        # Verify all updates were applied
        records = await service.load_all_records()
        statuses = {r.row_index: r.status for r in records}
        
        assert statuses[1] == "dialing"
        assert statuses[2] == "ringing"
        assert statuses[3] == "active"
    
    @pytest.mark.asyncio
    async def test_error_handling_invalid_file(self):
        """Test error handling with invalid file path"""
        # Use a path that can't be written to
        invalid_path = Path("/invalid/path/test.csv")
        service = CSVCallQueueDataService(invalid_path)
        
        # Should handle errors gracefully
        with pytest.raises(Exception):
            await service.load_all_records()
    
    @pytest.mark.asyncio
    async def test_update_nonexistent_record(self, temp_csv_file):
        """Test updating a record that doesn't exist"""
        service = CSVCallQueueDataService(temp_csv_file)
        
        # Try to update non-existent record
        result = await service.update_record_status(999, "test", "test")
        assert result is False
        
        result = await service.update_record_answer(999, "test")
        assert result is False
