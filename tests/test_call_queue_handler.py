import pytest
import async<PERSON>
from unittest.mock import AsyncMock, MagicMock, patch
from services.call_queue_handler import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CallItem, CallStatus


@pytest.fixture
def sample_call_item():
    """Create a sample call item for testing"""
    return CallItem(
        phone_number="+1234567890",
        metadata={"test": "data", "question": "How are you?"},
        call_id="test_call_001"
    )


@pytest.fixture
def call_handler():
    """Create a CallQueueHandler instance for testing"""
    return CallQueueHandler(
        agent_name="test-agent",
        sip_trunk_id="test-trunk",
        room_name_prefix="test-call-",
        monitor_timeout=10  # Short timeout for tests
    )


class TestCallItem:
    """Test CallItem functionality"""
    
    def test_call_item_creation(self, sample_call_item):
        """Test CallItem creation and default values"""
        assert sample_call_item.phone_number == "+1234567890"
        assert sample_call_item.call_id == "test_call_001"
        assert sample_call_item.status == CallStatus.PENDING
        assert sample_call_item.details == ""
        assert sample_call_item.metadata["test"] == "data"
    
    def test_to_json_metadata(self, sample_call_item):
        """Test JSON metadata conversion"""
        json_metadata = sample_call_item.to_json_metadata()
        
        # Should be valid JSON
        import json
        parsed = json.loads(json_metadata)
        
        # Should include phone number and call_id
        assert parsed["phone_number"] == "+1234567890"
        assert parsed["call_id"] == "test_call_001"
        
        # Should include original metadata
        assert parsed["test"] == "data"
        assert parsed["question"] == "How are you?"


class TestCallQueueHandler:
    """Test CallQueueHandler functionality"""
    
    def test_initialization(self, call_handler):
        """Test CallQueueHandler initialization"""
        assert call_handler.agent_name == "test-agent"
        assert call_handler.sip_trunk_id == "test-trunk"
        assert call_handler.room_name_prefix == "test-call-"
        assert call_handler.monitor_timeout == 10
        assert len(call_handler._active_calls) == 0
        assert len(call_handler._monitoring_tasks) == 0
    
    def test_get_room_name(self, call_handler):
        """Test room name generation"""
        room_name = call_handler._get_room_name("test_call_001")
        assert room_name == "test-call-test_call_001"
    
    @pytest.mark.asyncio
    async def test_status_callback_sync(self, sample_call_item):
        """Test status callback with synchronous function"""
        callback_calls = []
        
        def sync_callback(call_item, status, details):
            callback_calls.append((call_item.call_id, status, details))
        
        handler = CallQueueHandler(
            agent_name="test-agent",
            sip_trunk_id="test-trunk",
            status_callback=sync_callback
        )
        
        await handler._update_call_status(sample_call_item, CallStatus.DIALING, "test details")
        
        assert len(callback_calls) == 1
        assert callback_calls[0] == ("test_call_001", CallStatus.DIALING, "test details")
        assert sample_call_item.status == CallStatus.DIALING
        assert sample_call_item.details == "test details"
    
    @pytest.mark.asyncio
    async def test_status_callback_async(self, sample_call_item):
        """Test status callback with asynchronous function"""
        callback_calls = []
        
        async def async_callback(call_item, status, details):
            callback_calls.append((call_item.call_id, status, details))
        
        handler = CallQueueHandler(
            agent_name="test-agent",
            sip_trunk_id="test-trunk",
            status_callback=async_callback
        )
        
        await handler._update_call_status(sample_call_item, CallStatus.ACTIVE, "call answered")
        
        assert len(callback_calls) == 1
        assert callback_calls[0] == ("test_call_001", CallStatus.ACTIVE, "call answered")
    
    @pytest.mark.asyncio
    async def test_status_callback_exception_handling(self, sample_call_item):
        """Test that callback exceptions don't break the handler"""
        def failing_callback(call_item, status, details):
            raise Exception("Callback failed!")
        
        handler = CallQueueHandler(
            agent_name="test-agent",
            sip_trunk_id="test-trunk",
            status_callback=failing_callback
        )
        
        # Should not raise exception
        await handler._update_call_status(sample_call_item, CallStatus.ERROR, "test")
        
        # Status should still be updated
        assert sample_call_item.status == CallStatus.ERROR
    
    @pytest.mark.asyncio
    @patch('services.call_queue_handler.RoomsApiService')
    async def test_make_call_success(self, mock_rooms_service, call_handler, sample_call_item):
        """Test successful call creation"""
        # Mock the RoomsApiService
        mock_service_instance = AsyncMock()
        mock_rooms_service.return_value.__aenter__.return_value = mock_service_instance
        
        # Mock successful API calls
        mock_service_instance.create_agent_dispatch.return_value = {"id": "dispatch_123"}
        mock_service_instance.create_sip_participant.return_value = {"id": "participant_123"}
        
        # Mock the monitoring task creation
        with patch('asyncio.create_task') as mock_create_task:
            mock_task = AsyncMock()
            mock_create_task.return_value = mock_task
            
            result = await call_handler.make_call(sample_call_item)
            
            assert result is True
            assert sample_call_item.call_id in call_handler._active_calls
            assert sample_call_item.call_id in call_handler._monitoring_tasks
            
            # Verify API calls were made
            mock_service_instance.create_agent_dispatch.assert_called_once()
            mock_service_instance.create_sip_participant.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('services.call_queue_handler.RoomsApiService')
    async def test_make_call_failure(self, mock_rooms_service, call_handler, sample_call_item):
        """Test call creation failure"""
        # Mock the RoomsApiService to raise an exception
        mock_service_instance = AsyncMock()
        mock_rooms_service.return_value.__aenter__.return_value = mock_service_instance
        mock_service_instance.create_agent_dispatch.side_effect = Exception("API Error")
        
        result = await call_handler.make_call(sample_call_item)
        
        assert result is False
        assert sample_call_item.status == CallStatus.ERROR
        assert "Failed to initiate call" in sample_call_item.details
    
    @pytest.mark.asyncio
    async def test_process_call_queue_empty(self, call_handler):
        """Test processing empty call queue"""
        result = await call_handler.process_call_queue([])
        assert result == []
    
    @pytest.mark.asyncio
    async def test_cancel_all_calls(self, call_handler):
        """Test canceling all active calls"""
        # Add some mock tasks
        mock_task1 = AsyncMock()
        mock_task2 = AsyncMock()
        mock_task1.done.return_value = False
        mock_task2.done.return_value = False
        
        call_handler._monitoring_tasks = {
            "call1": mock_task1,
            "call2": mock_task2
        }
        call_handler._active_calls = {
            "call1": MagicMock(),
            "call2": MagicMock()
        }
        
        await call_handler.cancel_all_calls()
        
        # Verify tasks were canceled
        mock_task1.cancel.assert_called_once()
        mock_task2.cancel.assert_called_once()
        
        # Verify cleanup
        assert len(call_handler._monitoring_tasks) == 0
        assert len(call_handler._active_calls) == 0
    
    def test_get_active_calls(self, call_handler, sample_call_item):
        """Test getting active calls"""
        call_handler._active_calls["test_call"] = sample_call_item
        
        active_calls = call_handler.get_active_calls()
        
        assert "test_call" in active_calls
        assert active_calls["test_call"] == sample_call_item
        
        # Should return a copy, not the original dict
        assert active_calls is not call_handler._active_calls


class TestCallStatus:
    """Test CallStatus enum"""
    
    def test_call_status_values(self):
        """Test that all expected status values exist"""
        expected_statuses = [
            "pending", "dialing", "ringing", "active", 
            "completed", "timeout", "error"
        ]
        
        for status_value in expected_statuses:
            # Should be able to find status by value
            status = CallStatus(status_value)
            assert status.value == status_value
    
    def test_call_status_enum_members(self):
        """Test specific enum members"""
        assert CallStatus.PENDING.value == "pending"
        assert CallStatus.DIALING.value == "dialing"
        assert CallStatus.RINGING.value == "ringing"
        assert CallStatus.ACTIVE.value == "active"
        assert CallStatus.COMPLETED.value == "completed"
        assert CallStatus.TIMEOUT.value == "timeout"
        assert CallStatus.ERROR.value == "error"
