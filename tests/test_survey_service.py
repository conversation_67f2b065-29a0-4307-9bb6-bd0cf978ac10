import pytest
import asyncio
import tempfile
import yaml
from pathlib import Path
from datetime import datetime
from services.survey_service import (
    SurveyService, SurveyDefinition, SurveyQuestion, UserAnswers, UserAnswer
)


@pytest.fixture
def temp_surveys_dir():
    """Create a temporary directory with test survey files"""
    with tempfile.TemporaryDirectory() as temp_dir:
        surveys_dir = Path(temp_dir)
        
        # Create test survey YAML
        test_survey = {
            'title': 'Test Survey',
            'description': 'A test survey',
            'introduction': 'Welcome to our test survey',
            'conclusion': 'Thank you for participating',
            'max_duration_minutes': 5,
            'questions': [
                {
                    'id': 'q1',
                    'text': 'What is your favorite color?',
                    'type': 'text',
                    'required': True,
                    'order': 1
                },
                {
                    'id': 'q2',
                    'text': 'How satisfied are you?',
                    'type': 'rating',
                    'options': ['1', '2', '3', '4', '5'],
                    'required': True,
                    'order': 2
                },
                {
                    'id': 'q3',
                    'text': 'Would you recommend us?',
                    'type': 'yes_no',
                    'options': ['Yes', 'No'],
                    'required': False,
                    'order': 3
                }
            ]
        }
        
        # Write test survey file
        with open(surveys_dir / 'test_survey.yaml', 'w') as f:
            yaml.dump(test_survey, f)
        
        yield surveys_dir


class TestSurveyQuestion:
    """Test SurveyQuestion functionality"""
    
    def test_survey_question_creation(self):
        """Test basic question creation"""
        question = SurveyQuestion(
            id="q1",
            text="What is your name?",
            type="text"
        )
        
        assert question.id == "q1"
        assert question.text == "What is your name?"
        assert question.type == "text"
        assert question.required is True
        assert question.order == 0
        assert question.options == []
    
    def test_multiple_choice_question(self):
        """Test multiple choice question creation"""
        question = SurveyQuestion(
            id="q2",
            text="Choose your favorite",
            type="multiple_choice",
            options=["Option A", "Option B", "Option C"]
        )
        
        assert question.type == "multiple_choice"
        assert len(question.options) == 3
        assert "Option A" in question.options
    
    def test_multiple_choice_validation(self):
        """Test that multiple choice questions require options"""
        with pytest.raises(ValueError):
            SurveyQuestion(
                id="q3",
                text="Choose one",
                type="multiple_choice"
                # Missing options
            )


class TestUserAnswer:
    """Test UserAnswer functionality"""
    
    def test_user_answer_creation(self):
        """Test basic answer creation"""
        answer = UserAnswer(question_id="q1")
        
        assert answer.question_id == "q1"
        assert answer.answer == ""
        assert answer.answered_at is None
        assert answer.is_complete is False
    
    def test_record_answer(self):
        """Test recording an answer"""
        answer = UserAnswer(question_id="q1")
        answer.record_answer("Blue")
        
        assert answer.answer == "Blue"
        assert answer.is_complete is True
        assert answer.answered_at is not None
        assert isinstance(answer.answered_at, datetime)


class TestUserAnswers:
    """Test UserAnswers functionality"""
    
    def test_user_answers_creation(self):
        """Test user answers session creation"""
        answers = UserAnswers(
            survey_name="test_survey",
            participant_id="user123"
        )
        
        assert answers.survey_name == "test_survey"
        assert answers.participant_id == "user123"
        assert answers.started_at is not None
        assert answers.completed_at is None
        assert answers.current_question_index == 0
        assert len(answers.answers) == 0
    
    def test_record_answer(self):
        """Test recording answers"""
        answers = UserAnswers(
            survey_name="test_survey",
            participant_id="user123"
        )
        
        answers.record_answer("q1", "Blue")
        
        assert "q1" in answers.answers
        assert answers.answers["q1"].answer == "Blue"
        assert answers.answers["q1"].is_complete is True
    
    def test_get_next_unanswered_question(self):
        """Test getting next unanswered question"""
        # Create test survey
        survey = SurveyDefinition(
            name="test",
            title="Test",
            questions=[
                SurveyQuestion(id="q1", text="Question 1", order=1),
                SurveyQuestion(id="q2", text="Question 2", order=2),
                SurveyQuestion(id="q3", text="Question 3", order=3)
            ]
        )
        
        answers = UserAnswers(
            survey_name="test",
            participant_id="user123"
        )
        
        # Should get first question
        next_q = answers.get_next_unanswered_question(survey)
        assert next_q.id == "q1"
        
        # Answer first question
        answers.record_answer("q1", "Answer 1")
        
        # Should get second question
        next_q = answers.get_next_unanswered_question(survey)
        assert next_q.id == "q2"
        
        # Answer all questions
        answers.record_answer("q2", "Answer 2")
        answers.record_answer("q3", "Answer 3")
        
        # Should get None (all answered)
        next_q = answers.get_next_unanswered_question(survey)
        assert next_q is None
    
    def test_is_complete(self):
        """Test survey completion check"""
        survey = SurveyDefinition(
            name="test",
            title="Test",
            questions=[
                SurveyQuestion(id="q1", text="Required Q1", required=True),
                SurveyQuestion(id="q2", text="Optional Q2", required=False),
                SurveyQuestion(id="q3", text="Required Q3", required=True)
            ]
        )
        
        answers = UserAnswers(
            survey_name="test",
            participant_id="user123"
        )
        
        # Not complete initially
        assert not answers.is_complete(survey)
        
        # Answer one required question
        answers.record_answer("q1", "Answer 1")
        assert not answers.is_complete(survey)
        
        # Answer optional question
        answers.record_answer("q2", "Answer 2")
        assert not answers.is_complete(survey)
        
        # Answer second required question
        answers.record_answer("q3", "Answer 3")
        assert answers.is_complete(survey)
    
    def test_completion_percentage(self):
        """Test completion percentage calculation"""
        survey = SurveyDefinition(
            name="test",
            title="Test",
            questions=[
                SurveyQuestion(id="q1", text="Required Q1", required=True),
                SurveyQuestion(id="q2", text="Required Q2", required=True),
                SurveyQuestion(id="q3", text="Optional Q3", required=False)
            ]
        )
        
        answers = UserAnswers(
            survey_name="test",
            participant_id="user123"
        )
        
        # 0% initially
        assert answers.get_completion_percentage(survey) == 0.0
        
        # 50% after one required question
        answers.record_answer("q1", "Answer 1")
        assert answers.get_completion_percentage(survey) == 50.0
        
        # Still 50% after optional question
        answers.record_answer("q3", "Answer 3")
        assert answers.get_completion_percentage(survey) == 50.0
        
        # 100% after all required questions
        answers.record_answer("q2", "Answer 2")
        assert answers.get_completion_percentage(survey) == 100.0


class TestSurveyService:
    """Test SurveyService functionality"""
    
    @pytest.mark.asyncio
    async def test_load_survey(self, temp_surveys_dir):
        """Test loading survey from YAML"""
        service = SurveyService(temp_surveys_dir)
        
        survey = await service.load_survey("test_survey")
        
        assert survey is not None
        assert survey.name == "test_survey"
        assert survey.title == "Test Survey"
        assert len(survey.questions) == 3
        assert survey.questions[0].id == "q1"
        assert survey.questions[1].type == "rating"
        assert survey.questions[2].type == "yes_no"
    
    @pytest.mark.asyncio
    async def test_load_nonexistent_survey(self, temp_surveys_dir):
        """Test loading non-existent survey"""
        service = SurveyService(temp_surveys_dir)
        
        survey = await service.load_survey("nonexistent")
        assert survey is None
    
    @pytest.mark.asyncio
    async def test_list_available_surveys(self, temp_surveys_dir):
        """Test listing available surveys"""
        service = SurveyService(temp_surveys_dir)
        
        surveys = await service.list_available_surveys()
        assert "test_survey" in surveys
    
    @pytest.mark.asyncio
    async def test_create_user_session(self, temp_surveys_dir):
        """Test creating user session"""
        service = SurveyService(temp_surveys_dir)
        
        session = await service.create_user_session("test_survey", "user123")
        
        assert session is not None
        assert session.survey_name == "test_survey"
        assert session.participant_id == "user123"
        assert len(session.answers) == 3  # Pre-created for all questions
    
    @pytest.mark.asyncio
    async def test_record_answer(self, temp_surveys_dir):
        """Test recording answers through service"""
        service = SurveyService(temp_surveys_dir)
        
        # Create session
        await service.create_user_session("test_survey", "user123")
        
        # Record answer
        success = await service.record_answer("test_survey", "user123", "q1", "Blue")
        assert success is True
        
        # Verify answer was recorded
        session = await service.get_user_session("test_survey", "user123")
        assert session.answers["q1"].answer == "Blue"
    
    @pytest.mark.asyncio
    async def test_get_next_question(self, temp_surveys_dir):
        """Test getting next question"""
        service = SurveyService(temp_surveys_dir)
        
        # Create session
        await service.create_user_session("test_survey", "user123")
        
        # Get first question
        question = await service.get_next_question("test_survey", "user123")
        assert question.id == "q1"
        
        # Answer first question
        await service.record_answer("test_survey", "user123", "q1", "Blue")
        
        # Get next question
        question = await service.get_next_question("test_survey", "user123")
        assert question.id == "q2"
    
    @pytest.mark.asyncio
    async def test_survey_completion(self, temp_surveys_dir):
        """Test survey completion detection"""
        service = SurveyService(temp_surveys_dir)
        
        # Create session
        await service.create_user_session("test_survey", "user123")
        
        # Not complete initially
        is_complete = await service.is_survey_complete("test_survey", "user123")
        assert is_complete is False
        
        # Answer required questions
        await service.record_answer("test_survey", "user123", "q1", "Blue")
        await service.record_answer("test_survey", "user123", "q2", "5")
        
        # Should be complete now (q3 is optional)
        is_complete = await service.is_survey_complete("test_survey", "user123")
        assert is_complete is True
    
    @pytest.mark.asyncio
    async def test_survey_progress(self, temp_surveys_dir):
        """Test getting survey progress"""
        service = SurveyService(temp_surveys_dir)
        
        # Create session
        await service.create_user_session("test_survey", "user123")
        
        # Get initial progress
        progress = await service.get_survey_progress("test_survey", "user123")
        
        assert progress["survey_name"] == "test_survey"
        assert progress["participant_id"] == "user123"
        assert progress["completion_percentage"] == 0.0
        assert progress["is_complete"] is False
        assert progress["total_questions"] == 3
        assert progress["answered_questions"] == 0
